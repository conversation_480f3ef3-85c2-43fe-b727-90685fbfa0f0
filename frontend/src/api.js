import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_BACKEND_API_URL;
const WS_BASE_URL = process.env.REACT_APP_BACKEND_WS_URL;

// 通用 API 调用函数
const callApi = async (endpoint, method = 'GET', data = null, headers = {}, responseType = 'json') => {
    const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
            'Content-Type': 'application/json',
            ...headers,
        },
        responseType,
        data,
    };

    try {
        const response = await axios(config);
        return response.data;
    } catch (error) {
        console.error(`API Call Failed: ${endpoint} - ${error.response ? JSON.stringify(error.response.data) : error.message}`);
        throw error.response ? error.response.data : new Error('Network error');
    }
};

// ====== 用户认证与信息 ======
export const authApi = {
    login: (username, password) => callApi('/auth/token', 'POST', { username, password }),
    register: (userData) => callApi('/auth/register', 'POST', userData),
    getMe: () => callApi('/users/me'),
    changePassword: (oldPassword, newPassword) => callApi('/auth/change-password', 'POST', { old_password: oldPassword, new_password: newPassword }),
    updateProfile: (profileData) => callApi('/users/me', 'PUT', profileData),
};

// ====== 文件上传与管理 (Minio) ======
export const materialApi = {
    getUploadCredentials: (courseId, originalFilename, fileType) =>
        callApi('/materials/upload-credentials', 'POST', { course_id: courseId, original_filename: originalFilename, file_type: fileType }),
    uploadFileToMinio: async (uploadUrl, file, fileType) => {
        try {
            await axios.put(uploadUrl, file, { headers: { 'Content-Type': fileType } });
            return true;
        } catch (error) {
            console.error('Direct Minio upload failed:', error.response ? error.response.data : error.message);
            throw error.response ? error.response.data : new Error('Minio upload failed');
        }
    },
    createMaterialRecord: (courseId, materialData) =>
        callApi(`/materials/${courseId}`, 'POST', materialData),
    getDownloadUrl: (materialId) => callApi(`/materials/${materialId}/download-url`),
    deleteMaterial: (materialId) => callApi(`/materials/${materialId}`, 'DELETE'),
    getCourseMaterials: (courseId) => callApi(`/materials/course/${courseId}`),
    searchMaterials: (queryText, topK, courseId) => callApi(`/materials/search`, 'POST', { query_text: queryText, top_k: topK, course_id: courseId }),
    updateMaterial: (materialId, updateData) => callApi(`/materials/${materialId}`, 'PUT', updateData),
};

// ====== 学生端功能 ======
export const studentApi = {
    getDashboardSummary: () => callApi('/students/me/dashboard_summary'),
    getStudentCourses: (skip = 0, limit = 100) => callApi(`/students/me/courses?skip=${skip}&limit=${limit}`),
    getStudentQuizzes: (skip = 0, limit = 100) => callApi(`/students/me/quizzes?skip=${skip}&limit=${limit}`),
    getNotifications: (skip = 0, limit = 10) => callApi(`/students/me/notifications?skip=${skip}&limit=${limit}`),
    markNotificationRead: (notificationId) => callApi(`/students/me/notifications/${notificationId}/mark_read`, 'PUT'),
    getCourseDetailForStudent: (courseId) => callApi(`/students/courses/${courseId}`),
    getMaterialDetailForStudent: (materialId) => callApi(`/students/materials/${materialId}`),
    updateMaterialProgress: (materialId, progressData) => callApi(`/students/materials/${materialId}/progress`, 'POST', progressData),
    getQuizForStudent: (quizId) => callApi(`/students/quizzes/${quizId}`),
    submitQuizAttempt: (quizId, attemptData) => callApi(`/students/quizzes/${quizId}/submit`, 'POST', attemptData),
    getQuizAttemptDetail: (attemptId) => callApi(`/students/quizzes/attempts/${attemptId}`),
};

// ====== 教师端功能 ======
export const teacherApi = {
    createCourse: (courseData) => callApi('/courses/', 'POST', courseData),
    getCourses: (skip = 0, limit = 100, search = '') => callApi(`/courses/?skip=${skip}&limit=${limit}${search ? `&search=${search}` : ''}`),
    getCourseDetail: (courseId) => callApi(`/courses/${courseId}`),
    updateCourse: (courseId, updateData) => callApi(`/courses/${courseId}`, 'PUT', updateData),
    deleteCourse: (courseId) => callApi(`/courses/${courseId}`, 'DELETE'),

    createClass: (classData) => callApi('/classes/', 'POST', classData),
    getClasses: (skip = 0, limit = 100, search = '') => callApi(`/classes/?skip=${skip}&limit=${limit}${search ? `&search=${search}` : ''}`),
    getClassDetail: (classId) => callApi(`/classes/${classId}`),
    updateClass: (classId, updateData) => callApi(`/classes/${classId}`, 'PUT', updateData),
    deleteClass: (classId) => callApi(`/classes/${classId}`, 'DELETE'),
    addStudentsToClass: (classId, studentIds) => callApi(`/classes/${classId}/students`, 'POST', { student_ids: studentIds }),
    removeStudentFromClass: (classId, studentId) => callApi(`/classes/${classId}/students/${studentId}`, 'DELETE'),
    getStudentsInClass: (classId, skip = 0, limit = 100) => callApi(`/classes/${classId}/students?skip=${skip}&limit=${limit}`),

    createQuiz: (quizData) => callApi('/quizzes/', 'POST', quizData),
    getQuizzesInCourse: (courseId, skip = 0, limit = 100) => callApi(`/quizzes/course/${courseId}?skip=${skip}&limit=${limit}`),
    getQuizDetail: (quizId) => callApi(`/quizzes/${quizId}`),
    updateQuiz: (quizId, updateData) => callApi(`/quizzes/${quizId}`, 'PUT', updateData),
    deleteQuiz: (quizId) => callApi(`/quizzes/${quizId}`, 'DELETE'),
    addQuestionToQuiz: (quizId, questionData) => callApi(`/quizzes/${quizId}/questions`, 'POST', questionData),
    getQuestionsForQuiz: (quizId) => callApi(`/quizzes/${quizId}/questions`),
    getQuestionDetail: (questionId) => callApi(`/quizzes/questions/${questionId}`),
    updateQuestion: (questionId, updateData) => callApi(`/quizzes/questions/${questionId}`, 'PUT', updateData),
    deleteQuestion: (questionId) => callApi(`/quizzes/questions/${questionId}`, 'DELETE'),
    getAllAttemptsForQuiz: (quizId, skip = 0, limit = 100) => callApi(`/quizzes/${quizId}/attempts?skip=${skip}&limit=${limit}`),
    getAttemptDetailForTeacher: (attemptId) => callApi(`/quizzes/attempts/${attemptId}`),
    gradeShortAnswer: (attemptId, questionId, score, feedback) => callApi(`/quizzes/attempts/${attemptId}/questions/${questionId}/grade`, 'PUT', { score, feedback }),

    getStudentDailyReport: (studentId, startDate, endDate) => callApi(`/reports/students/${studentId}/daily_study_duration?start_date=${startDate}&end_date=${endDate}`),
    getCourseReport: (courseId) => callApi(`/reports/courses/${courseId}`),
    getClassReport: (classId) => callApi(`/reports/classes/${classId}`),
};

// ====== 管理员功能 ======
export const adminApi = {
    createAdminUser: (userData) => callApi('/admin/users', 'POST', userData), // Admin can create users of any role
    getAllUsers: (skip = 0, limit = 100, search = '', role = '') => callApi(`/admin/users?skip=${skip}&limit=${limit}${search ? `&search=${search}` : ''}${role ? `&role=${role}` : ''}`),
    getUserById: (userId) => callApi(`/admin/users/${userId}`),
    updateUser: (userId, updateData) => callApi(`/admin/users/${userId}`, 'PUT', updateData),
    deleteUser: (userId) => callApi(`/admin/users/${userId}`, 'DELETE'),

    getAllCoursesAdmin: (skip = 0, limit = 100, search = '') => callApi(`/admin/courses?skip=${skip}&limit=${limit}${search ? `&search=${search}` : ''}`),
    updateCourseAdmin: (courseId, updateData) => callApi(`/admin/courses/${courseId}`, 'PUT', updateData),
    deleteCourseAdmin: (courseId) => callApi(`/admin/courses/${courseId}`, 'DELETE'),

    getAllClassesAdmin: (skip = 0, limit = 100, search = '') => callApi(`/admin/classes?skip=${skip}&limit=${limit}${search ? `&search=${search}` : ''}`),
    updateClassAdmin: (classId, updateData) => callApi(`/admin/classes/${classId}`, 'PUT', updateData),
    deleteClassAdmin: (classId) => callApi(`/admin/classes/${classId}`, 'DELETE'),

    deleteMaterialAdmin: (materialId) => callApi(`/admin/materials/${materialId}`, 'DELETE'),
    deleteQuizAdmin: (quizId) => callApi(`/admin/quizzes/${quizId}`, 'DELETE'),
};

// ====== WebSocket 客户端 ======
export const websocketService = {
    connect: (token, onMessageCallback) => {
        const socket = new WebSocket(`${WS_BASE_URL}/notifications?token=${token}`);

        socket.onopen = () => console.log('WebSocket connected.');
        socket.onmessage = (event) => onMessageCallback(JSON.parse(event.data));
        socket.onclose = (event) => console.warn('WebSocket disconnected:', event.code, event.reason);
        socket.onerror = (error) => console.error('WebSocket error:', error);

        // Return the WebSocket instance for external management (e.g., closing)
        return socket;
    },
    // You might add send methods if clients need to send messages to backend via WS
};