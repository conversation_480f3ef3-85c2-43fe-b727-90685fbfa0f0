import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true); // 用于初始化加载

  const API_BASE_URL = process.env.REACT_APP_BACKEND_API_URL;

  // Axios 拦截器，用于在所有请求中添加 Token
  axios.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('jwtToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Axios 拦截器，用于处理 401 Unauthorized 错误
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.status === 401) {
        console.error('Unauthorized request, logging out...');
        logout(); // 自动登出
      }
      return Promise.reject(error);
    }
  );

  const fetchCurrentUser = async () => {
    try {
      const token = localStorage.getItem('jwtToken');
      if (token) {
        const response = await axios.get(`${API_BASE_URL}/users/me`);
        setUser(response.data);
        setUserRole(response.data.role);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Failed to fetch current user:', error);
      localStorage.removeItem('jwtToken'); // Token 无效，清除它
      setIsAuthenticated(false);
      setUser(null);
      setUserRole(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCurrentUser();
  }, []);

  const login = async (username, password) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/token`, { username, password });
      localStorage.setItem('jwtToken', response.data.access_token);
      await fetchCurrentUser(); // 重新获取用户数据
      return response.data;
    } catch (error) {
      console.error('Login failed:', error.response ? error.response.data : error.message);
      throw error.response ? error.response.data : new Error('Login failed');
    }
  };

  const register = async (userData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, userData);
      return response.data;
    } catch (error) {
      console.error('Registration failed:', error.response ? error.response.data : error.message);
      throw error.response ? error.response.data : new Error('Registration failed');
    }
  };

  const logout = () => {
    localStorage.removeItem('jwtToken');
    setIsAuthenticated(false);
    setUser(null);
    setUserRole(null);
  };

  if (loading) {
    return <div>Loading authentication...</div>; // 显示加载状态
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, userRole, login, register, logout, fetchCurrentUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);