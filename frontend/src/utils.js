// frontend/src/utils.js

export const formatDateTime = (isoString) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleString(); // 或 date.toISOString().split('T')[0] for date only
};

export const formatDuration = (seconds) => {
    if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
};

export const getFileTypeIcon = (fileType) => {
    switch (fileType) {
        case 'video': return '🎥';
        case 'image': return '🖼️';
        case 'ppt': return '📊';
        case 'pdf': return '📄';
        case 'word': return '📝';
        case 'audio': return '🎧';
        case 'text': return '📃';
        default: return '❓';
    }
};

export const getQuizTypeDisplay = (quizType) => {
    switch (quizType) {
        case 'quiz': return '测验';
        case 'assignment': return '作业';
        default: return quizType;
    }
};

export const getQuestionTypeDisplay = (questionType) => {
    switch (questionType) {
        case 'single_choice': return '单选题';
        case 'multiple_choice': return '多选题';
        case 'fill_in_blank': return '填空题';
        case 'short_answer': return '问答题';
        default: return questionType;
    }
};

export const getNotificationTypeDisplay = (type) => {
    switch (type) {
        case 'personal': return '个人消息';
        case 'class': return '班级公告';
        case 'course': return '课程通知';
        case 'system': return '系统通知';
        default: return type;
    }
};

// Minio文件类型到后端枚举的映射
export const mapMimeToMaterialType = (mimeType) => {
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('powerpoint')) return 'ppt'; // application/vnd.ms-powerpoint or application/vnd.openxmlformats-officedocument.presentationml.presentation
    if (mimeType.includes('pdf')) return 'pdf'; // application/pdf
    if (mimeType.includes('word')) return 'word'; // application/msword or application/vnd.openxmlformats-officedocument.wordprocessingml.document
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/')) return 'text';
    return 'text'; // Default fallback
};