import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

function Auth({ type }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [fullName, setFullName] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { login, register, isAuthenticated, userRole } = useAuth();

  React.useEffect(() => {
    if (isAuthenticated) {
      navigate(userRole === 'student' ? '/student' : '/teacher', { replace: true });
    }
  }, [isAuthenticated, userRole, navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (type === 'login') {
      try {
        await login(username, password);
        setSuccess('登录成功！');
      } catch (err) {
        setError(err.detail || '登录失败');
      }
    } else { // register
      try {
        await register({ username, password, email, full_name: fullName });
        setSuccess('注册成功！请登录。');
        setUsername('');
        setPassword('');
        setEmail('');
        setFullName('');
      } catch (err) {
        setError(err.detail || '注册失败');
      }
    }
  };

  return (
    <div className="container">
      <h2>{type === 'login' ? '登录' : '注册'}</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <label>用户名:</label>
          <input type="text" value={username} onChange={(e) => setUsername(e.target.value)} required />
        </div>
        <div>
          <label>密码:</label>
          <input type="password" value={password} onChange={(e) => setPassword(e.target.value)} required />
        </div>
        {type === 'register' && (
          <>
            <div>
              <label>邮箱:</label>
              <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
            </div>
            <div>
              <label>姓名:</label>
              <input type="text" value={fullName} onChange={(e) => setFullName(e.target.value)} />
            </div>
          </>
        )}
        {error && <p className="error">{error}</p>}
        {success && <p className="success">{success}</p>}
        <button type="submit">{type === 'login' ? '登录' : '注册'}</button>
      </form>
    </div>
  );
}

export default Auth;