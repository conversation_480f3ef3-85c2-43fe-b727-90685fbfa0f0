import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { studentApi } from '../api';
import { getFileTypeIcon, getQuizTypeDisplay } from '../utils';

function CourseDetail() {
  const { courseId } = useParams();
  const [courseDetail, setCourseDetail] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchCourseDetail = async () => {
      try {
        const data = await studentApi.getCourseDetailForStudent(courseId);
        setCourseDetail(data);
        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch course details');
        setLoading(false);
      }
    };

    fetchCourseDetail();
  }, [courseId]);

  if (loading) return <div>Loading course details...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!courseDetail) return <div>No course data available.</div>;

  return (
    <div className="container">
      <h2>{courseDetail.title}</h2>
      <p><strong>教师:</strong> {courseDetail.teacher_name}</p>
      <p><strong>描述:</strong> {courseDetail.description}</p>
      <p><strong>状态:</strong> {courseDetail.status}</p>

      <h3>学习资料</h3>
      {courseDetail.materials.length === 0 ? (
        <p>此课程暂无学习资料。</p>
      ) : (
        <ul>
          {courseDetail.materials.map((material) => (
            <li key={material.id} className="material-item">
              <span>
                {getFileTypeIcon(material.file_type)} <Link to={`/student/materials/${material.id}`}>{material.title}</Link> ({material.my_progress_percentage}% 完成)
              </span>
              <progress value={material.my_progress_percentage} max="100"></progress>
            </li>
          ))}
        </ul>
      )}

      <h3>测验/作业</h3>
      {courseDetail.quizzes.length === 0 ? (
        <p>此课程暂无测验或作业。</p>
      ) : (
        <ul>
          {courseDetail.quizzes.map((quiz) => (
            <li key={quiz.id} className="quiz-item">
              {quiz.is_completed_by_me ? (
                <Link to={`/student/quizzes/results/${quiz.id}`}>{quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)}) - 已完成 (得分: {quiz.my_score})</Link>
              ) : (
                <Link to={`/student/quizzes/${quiz.id}`}>{quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)}) - 未完成</Link>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default CourseDetail;