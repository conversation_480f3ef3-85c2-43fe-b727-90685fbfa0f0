import React, { useEffect, useState } from 'react';
import { teacherApi } from '../api';
import { formatDateTime, formatDuration } from '../utils';

function ReportViewer() {
  const [courses, setCourses] = useState([]);
  const [classes, setClasses] = useState([]);
  const [selectedCourseId, setSelectedCourseId] = useState('');
  const [selectedClassId, setSelectedClassId] = useState('');

  const [courseReport, setCourseReport] = useState(null);
  const [classReport, setClassReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const [studentDailyReport, setStudentDailyReport] = useState(null);
  const [selectedStudentId, setSelectedStudentId] = useState('');
  const [allStudents, setAllStudents] = useState([]); // For student selection in daily report
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [dailyReportError, setDailyReportError] = useState('');

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const fetchedCourses = await teacherApi.getCourses(); // Teacher can only see their courses
        const fetchedClasses = await teacherApi.getClasses(); // Teacher can only see their classes
        const allPlatformUsers = await teacherApi.getAllUsers(); // Assuming teacher can view all users for reporting if implemented

        setCourses(fetchedCourses);
        setClasses(fetchedClasses);
        setAllStudents(allPlatformUsers.filter(u => u.role === 'student')); // Filter out students

        if (fetchedCourses.length > 0) setSelectedCourseId(fetchedCourses[0].id);
        if (fetchedClasses.length > 0) setSelectedClassId(fetchedClasses[0].id);
        if (allPlatformUsers.length > 0) setSelectedStudentId(allPlatformUsers.filter(u => u.role === 'student')[0]?.id || '');

        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch initial data for reports');
        setLoading(false);
      }
    };
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedCourseId) {
      fetchCourseReport(selectedCourseId);
    } else {
      setCourseReport(null);
    }
  }, [selectedCourseId]);

  useEffect(() => {
    if (selectedClassId) {
      fetchClassReport(selectedClassId);
    } else {
      setClassReport(null);
    }
  }, [selectedClassId]);

  const fetchCourseReport = async (courseId) => {
    try {
      const report = await teacherApi.getCourseReport(courseId);
      setCourseReport(report);
    } catch (err) {
      setCourseReport(null);
      setError(err.detail || 'Failed to fetch course report');
    }
  };

  const fetchClassReport = async (classId) => {
    try {
      const report = await teacherApi.getClassReport(classId);
      setClassReport(report);
    } catch (err) {
      setClassReport(null);
      setError(err.detail || 'Failed to fetch class report');
    }
  };

  const handleFetchStudentDailyReport = async (e) => {
    e.preventDefault();
    setDailyReportError('');
    setStudentDailyReport(null);
    if (!selectedStudentId || !startDate || !endDate) {
      setDailyReportError('请选择学生和日期范围。');
      return;
    }
    try {
      const report = await teacherApi.getStudentDailyReport(selectedStudentId, startDate, endDate);
      setStudentDailyReport(report);
    } catch (err) {
      setDailyReportError(err.detail || 'Failed to fetch student daily report');
    }
  };

  if (loading) return <div>Loading reports data...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>学习报告</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>课程学习报告</h3>
        <div>
          <label>选择课程:</label>
          <select value={selectedCourseId} onChange={(e) => setSelectedCourseId(e.target.value)}>
            <option value="">-- 选择课程 --</option>
            {courses.map(course => (
              <option key={course.id} value={course.id}>{course.title}</option>
            ))}
          </select>
        </div>
        {courseReport && (
          <div style={{ marginTop: '15px' }}>
            <p><strong>课程标题:</strong> {courseReport.course_title}</p>
            <p><strong>总学生数:</strong> {courseReport.total_students}</p>
            <p><strong>平均完成度:</strong> {courseReport.average_completion_percentage}%</p>

            <h4>资料完成率</h4>
            {courseReport.material_completion_rates.length === 0 ? <p>无资料完成数据。</p> : (
              <ul>
                {courseReport.material_completion_rates.map(m => (
                  <li key={m.material_id}>{m.material_title}: {m.completion_percentage}% ({m.completed_students_count}人完成)</li>
                ))}
              </ul>
            )}

            <h4>测验成绩分布</h4>
            {courseReport.quiz_score_distribution.length === 0 ? <p>无测验成绩数据。</p> : (
              <ul>
                {courseReport.quiz_score_distribution.map(s => (
                  <li key={s.score_range}>{s.score_range}: {s.student_count}人 ({s.percentage}%)</li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>班级学习报告</h3>
        <div>
          <label>选择班级:</label>
          <select value={selectedClassId} onChange={(e) => setSelectedClassId(e.target.value)}>
            <option value="">-- 选择班级 --</option>
            {classes.map(cls => (
              <option key={cls.id} value={cls.id}>{cls.name}</option>
            ))}
          </select>
        </div>
        {classReport && (
          <div style={{ marginTop: '15px' }}>
            <p><strong>班级名称:</strong> {classReport.class_name}</p>
            <p><strong>教师:</strong> {classReport.teacher_name}</p>
            <p><strong>总学生数:</strong> {classReport.total_students}</p>
            <p><strong>班级平均学习时长:</strong> {formatDuration(classReport.average_class_study_duration_seconds)}</p>
            <p><strong>班级平均测验成绩:</strong> {classReport.average_class_quiz_score !== null ? classReport.average_class_quiz_score : 'N/A'}</p>

            <h4>学生概览</h4>
            {classReport.student_overviews.length === 0 ? <p>班级中没有学生数据。</p> : (
              <ul>
                {classReport.student_overviews.map(s => (
                  <li key={s.student_id}>
                    {s.student_name} (ID: {s.student_id}) -
                    课程数: {s.total_courses_enrolled},
                    平均课程进度: {s.average_course_progress}%,
                    总学习时长: {formatDuration(s.total_study_duration_seconds)},
                    平均测验分: {s.average_quiz_score !== null ? s.average_quiz_score : 'N/A'}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}
      </div>

      <div className="dashboard-card">
        <h3>学生每日学习时长报告</h3>
        <form onSubmit={handleFetchStudentDailyReport}>
          <div>
            <label>选择学生:</label>
            <select value={selectedStudentId} onChange={(e) => setSelectedStudentId(e.target.value)} required>
              <option value="">-- 选择学生 --</option>
              {allStudents.map(student => (
                <option key={student.id} value={student.id}>{student.full_name || student.username} (ID: {student.id})</option>
              ))}
            </select>
          </div>
          <div>
            <label>开始日期:</label>
            <input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} required />
          </div>
          <div>
            <label>结束日期:</label>
            <input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} required />
          </div>
          {dailyReportError && <p className="error">{dailyReportError}</p>}
          <button type="submit">获取报告</button>
        </form>
        {studentDailyReport && studentDailyReport.length > 0 ? (
          <div style={{ marginTop: '15px' }}>
            <h4>每日学习时长</h4>
            <ul>
              {studentDailyReport.map(item => (
                <li key={item.date}>{formatDateTime(item.date)}: {formatDuration(item.duration_seconds)}</li>
              ))}
            </ul>
          </div>
        ) : studentDailyReport && studentDailyReport.length === 0 ? (
          <p>该学生在所选日期范围内没有学习数据。</p>
        ) : null}
      </div>
    </div>
  );
}

export default ReportViewer;