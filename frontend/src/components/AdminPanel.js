import React, { useState, useEffect } from 'react';
import { adminApi, teacherApi } from '../api'; // Admin can also use teacherApi functions
import { useAuth } from '../contexts/AuthContext'; // To get current admin user
import { formatDateTime } from '../utils';

function AdminPanel() {
  const { user: currentAdminUser, fetchCurrentUser } = useAuth();
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [userError, setUserError] = useState('');

  const [editUserId, setEditUserId] = useState(null);
  const [editUsername, setEditUsername] = useState('');
  const [editEmail, setEditEmail] = useState('');
  const [editFullName, setEditFullName] = useState('');
  const [editRole, setEditRole] = useState('');
  const [editIsActive, setEditIsActive] = useState(true);
  const [editPassword, setEditPassword] = useState(''); // For admin to reset password
  const [editUserError, setEditUserError] = useState('');
  const [editUserSuccess, setEditUserSuccess] = useState('');

  const [createUsername, setCreateUsername] = useState('');
  const [createPassword, setCreatePassword] = useState('');
  const [createEmail, setCreateEmail] = useState('');
  const [createFullName, setCreateFullName] = useState('');
  const [createRole, setCreateRole] = useState('student');
  const [createUserError, setCreateUserError] = useState('');
  const [createUserSuccess, setCreateUserSuccess] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoadingUsers(true);
    setUserError('');
    try {
      const data = await adminApi.getAllUsers();
      setUsers(data);
    } catch (err) {
      setUserError(err.detail || 'Failed to fetch users');
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleEditClick = (user) => {
    setEditUserId(user.id);
    setEditUsername(user.username);
    setEditEmail(user.email || '');
    setEditFullName(user.full_name || '');
    setEditRole(user.role);
    setEditIsActive(user.is_active);
    setEditPassword(''); // Clear password field
    setEditUserError('');
    setEditUserSuccess('');
  };

  const handleUpdateUser = async (e) => {
    e.preventDefault();
    setEditUserError('');
    setEditUserSuccess('');
    try {
      const updateData = {
        username: editUsername,
        email: editEmail,
        full_name: editFullName,
        role: editRole,
        is_active: editIsActive,
      };
      if (editPassword) {
        updateData.password = editPassword; // Admin can reset password directly
      }
      await adminApi.updateUser(editUserId, updateData);
      setEditUserSuccess('用户更新成功！');
      setEditUserId(null); // Close edit form
      await fetchUsers(); // Refresh user list
      if (editUserId === currentAdminUser.id) { // If admin updated self, refresh auth context
        await fetchCurrentUser();
      }
    } catch (err) {
      setEditUserError(err.detail || '更新用户失败');
    }
  };

  const handleDeleteUser = async (userId) => {
    if (userId === currentAdminUser.id) {
        alert("不能删除自己的账户！");
        return;
    }
    if (window.confirm(`确定删除用户ID: ${userId} 吗？此操作不可逆！`)) {
      try {
        await adminApi.deleteUser(userId);
        alert('用户删除成功！');
        await fetchUsers();
      } catch (err) {
        alert('删除用户失败: ' + (err.detail || err.message));
      }
    }
  };

  const handleCreateUser = async (e) => {
    e.preventDefault();
    setCreateUserError('');
    setCreateUserSuccess('');
    try {
      const userData = {
        username: createUsername,
        password: createPassword,
        email: createEmail,
        full_name: createFullName,
        role: createRole,
      };
      await adminApi.createAdminUser(userData);
      setCreateUserSuccess('用户创建成功！');
      setCreateUsername('');
      setCreatePassword('');
      setCreateEmail('');
      setCreateFullName('');
      setCreateRole('student');
      await fetchUsers();
    } catch (err) {
      setCreateUserError(err.detail || '创建用户失败');
    }
  };

  if (loadingUsers) return <div>Loading admin panel...</div>;
  if (userError) return <div className="error">{userError}</div>;

  return (
    <div className="container">
      <h2>管理员面板</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新用户</h3>
        <form onSubmit={handleCreateUser}>
          <div>
            <label>用户名:</label>
            <input type="text" value={createUsername} onChange={(e) => setCreateUsername(e.target.value)} required />
          </div>
          <div>
            <label>密码:</label>
            <input type="password" value={createPassword} onChange={(e) => setCreatePassword(e.target.value)} required />
          </div>
          <div>
            <label>邮箱:</label>
            <input type="email" value={createEmail} onChange={(e) => setCreateEmail(e.target.value)} />
          </div>
          <div>
            <label>姓名:</label>
            <input type="text" value={createFullName} onChange={(e) => setCreateFullName(e.target.value)} />
          </div>
          <div>
            <label>角色:</label>
            <select value={createRole} onChange={(e) => setCreateRole(e.target.value)}>
              <option value="student">学生</option>
              <option value="teacher">教师</option>
              <option value="admin">管理员</option>
            </select>
          </div>
          {createUserError && <p className="error">{createUserError}</p>}
          {createUserSuccess && <p className="success">{createUserSuccess}</p>}
          <button type="submit">创建用户</button>
        </form>
      </div>

      <h3>所有用户列表</h3>
      {users.length === 0 ? (
        <p>没有用户。</p>
      ) : (
        <ul>
          {users.map((user) => (
            <li key={user.id} className="material-item">
              {editUserId === user.id ? (
                // 编辑模式
                <form onSubmit={handleUpdateUser} style={{ width: '100%' }}>
                  <p><strong>编辑用户 ID: {user.id}</strong></p>
                  <div>
                    <label>用户名:</label>
                    <input type="text" value={editUsername} onChange={(e) => setEditUsername(e.target.value)} />
                  </div>
                  <div>
                    <label>邮箱:</label>
                    <input type="email" value={editEmail} onChange={(e) => setEditEmail(e.target.value)} />
                  </div>
                  <div>
                    <label>姓名:</label>
                    <input type="text" value={editFullName} onChange={(e) => setEditFullName(e.target.value)} />
                  </div>
                  <div>
                    <label>角色:</label>
                    <select value={editRole} onChange={(e) => setEditRole(e.target.value)}>
                      <option value="student">学生</option>
                      <option value="teacher">教师</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                  <div>
                    <label>
                      <input type="checkbox" checked={editIsActive} onChange={(e) => setEditIsActive(e.target.checked)} />
                      活跃
                    </label>
                  </div>
                  <div>
                    <label>重置密码 (留空不修改):</label>
                    <input type="password" value={editPassword} onChange={(e) => setEditPassword(e.target.value)} />
                  </div>
                  {editUserError && <p className="error">{editUserError}</p>}
                  {editUserSuccess && <p className="success">{editUserSuccess}</p>}
                  <button type="submit">保存</button>
                  <button type="button" onClick={() => setEditUserId(null)} style={{ backgroundColor: '#6c757d' }}>取消</button>
                </form>
              ) : (
                // 显示模式
                <>
                  <span>
                    <strong>{user.username}</strong> ({user.role}) - {user.full_name} ({user.email || 'N/A'}) {user.is_active ? '' : '(非活跃)'}
                  </span>
                  <div>
                    <button onClick={() => handleEditClick(user)}>编辑</button>
                    <button onClick={() => handleDeleteUser(user.id)} style={{ backgroundColor: 'red' }}>删除</button>
                  </div>
                </>
              )}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default AdminPanel;