import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { teacherApi, authApi } from '../api';
import { formatDateTime, getQuizTypeDisplay, getQuestionTypeDisplay } from '../utils';

function QuizManagement() {
  const { courseId: routeCourseId } = useParams(); // 从路由获取可选的 courseId
  const [courses, setCourses] = useState([]);
  const [selectedCourseId, setSelectedCourseId] = useState(routeCourseId || ''); // 选中的课程
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const [newQuizTitle, setNewQuizTitle] = useState('');
  const [newQuizDesc, setNewQuizDesc] = useState('');
  const [newQuizType, setNewQuizType] = useState('quiz');
  const [newQuizDueDate, setNewQuizDueDate] = useState('');
  const [newQuizIsPublished, setNewQuizIsPublished] = useState(false);
  const [createQuizError, setCreateQuizError] = useState('');

  const [selectedQuizIdForQuestions, setSelectedQuizIdForQuestions] = useState(null);
  const [questionsInQuiz, setQuestionsInQuiz] = useState([]);
  const [showQuestionsModal, setShowQuestionsModal] = useState(false);
  const [newQuestionText, setNewQuestionText] = useState('');
  const [newQuestionType, setNewQuestionType] = useState('single_choice');
  const [newQuestionOptions, setNewQuestionOptions] = useState(''); // JSON string for options
  const [newQuestionCorrectAnswer, setNewQuestionCorrectAnswer] = useState(''); // JSON string for answer
  const [newQuestionScore, setNewQuestionScore] = useState(1);
  const [addQuestionError, setAddQuestionError] = useState('');

  const [selectedAttemptId, setSelectedAttemptId] = useState(null);
  const [selectedAttemptDetail, setSelectedAttemptDetail] = useState(null);
  const [showAttemptModal, setShowAttemptModal] = useState(false);
  const [gradeScore, setGradeScore] = useState(0);
  const [gradeFeedback, setGradeFeedback] = useState('');
  const [gradingError, setGradingError] = useState('');
  const [currentGradingQuestionId, setCurrentGradingQuestionId] = useState(null);

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const teacherCourses = await teacherApi.getCourses();
        setCourses(teacherCourses);
        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch initial data');
        setLoading(false);
      }
    };
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedCourseId) {
      fetchQuizzes(selectedCourseId);
    } else {
      setQuizzes([]); // Clear quizzes if no course selected
    }
  }, [selectedCourseId]);

  const fetchQuizzes = async (courseId) => {
    try {
      const data = await teacherApi.getQuizzesInCourse(courseId);
      setQuizzes(data);
    } catch (err) {
      setError(err.detail || 'Failed to fetch quizzes');
    }
  };

  const handleCreateQuiz = async (e) => {
    e.preventDefault();
    setCreateQuizError('');
    if (!selectedCourseId) {
      setCreateQuizError('请选择所属课程');
      return;
    }
    try {
      const quizData = {
        course_id: parseInt(selectedCourseId),
        title: newQuizTitle,
        description: newQuizDesc,
        quiz_type: newQuizType,
        due_date: newQuizDueDate ? new Date(newQuizDueDate).toISOString() : null,
        is_published: newQuizIsPublished,
      };
      await teacherApi.createQuiz(quizData);
      setNewQuizTitle('');
      setNewQuizDesc('');
      setNewQuizDueDate('');
      setNewQuizIsPublished(false);
      await fetchQuizzes(selectedCourseId);
      alert('测验创建成功！');
    } catch (err) {
      setCreateQuizError(err.detail || '创建测验失败');
    }
  };

  const handleDeleteQuiz = async (quizId) => {
    if (window.confirm('确定删除此测验及其所有问题和学生尝试吗？此操作不可逆！')) {
      try {
        await teacherApi.deleteQuiz(quizId);
        await fetchQuizzes(selectedCourseId);
        alert('测验删除成功！');
      } catch (err) {
        alert('删除测验失败: ' + (err.detail || err.message));
      }
    }
  };

  // Question Management
  const fetchQuestions = async (quizId) => {
    try {
      const data = await teacherApi.getQuestionsForQuiz(quizId);
      setQuestionsInQuiz(data);
    } catch (err) {
      setAddQuestionError(err.detail || '无法获取问题列表');
    }
  };

  const handleAddQuestion = async (e) => {
    e.preventDefault();
    setAddQuestionError('');
    try {
      let optionsParsed = null;
      if (newQuestionOptions) {
        try { optionsParsed = JSON.parse(newQuestionOptions); }
        catch { throw new Error("选项必须是有效的JSON格式。"); }
      }

      let correctAnswerParsed = null;
      if (newQuestionCorrectAnswer) {
        try { correctAnswerParsed = JSON.parse(newQuestionCorrectAnswer); }
        catch { correctAnswerParsed = newQuestionCorrectAnswer; } // For string answers like fill-in-blank
      }

      const questionData = {
        question_text: newQuestionText,
        question_type: newQuestionType,
        options: optionsParsed,
        correct_answer: correctAnswerParsed,
        score: parseInt(newQuestionScore),
      };
      await teacherApi.addQuestionToQuiz(selectedQuizIdForQuestions, questionData);
      setNewQuestionText('');
      setNewQuestionOptions('');
      setNewQuestionCorrectAnswer('');
      setNewQuestionScore(1);
      await fetchQuestions(selectedQuizIdForQuestions);
      alert('问题添加成功！');
    } catch (err) {
      setAddQuestionError(err.detail || '添加问题失败');
    }
  };

  const handleDeleteQuestion = async (questionId) => {
    if (window.confirm('确定删除此问题吗？')) {
      try {
        await teacherApi.deleteQuestion(questionId);
        await fetchQuestions(selectedQuizIdForQuestions);
        alert('问题删除成功！');
      } catch (err) {
        alert('删除问题失败: ' + (err.detail || err.message));
      }
    }
  };

  const handleViewQuestions = async (quizId) => {
    setSelectedQuizIdForQuestions(quizId);
    await fetchQuestions(quizId);
    setShowQuestionsModal(true);
  };

  // Attempt Management
  const fetchAttemptDetail = async (attemptId) => {
    try {
      const data = await teacherApi.getAttemptDetailForTeacher(attemptId);
      setSelectedAttemptDetail(data);
    } catch (err) {
      setGradingError(err.detail || '无法获取尝试详情');
    }
  };

  const handleViewAttempt = async (attemptId) => {
    setSelectedAttemptId(attemptId);
    await fetchAttemptDetail(attemptId);
    setShowAttemptModal(true);
  };

  const handleGradeShortAnswer = async (attemptId, questionId) => {
    setGradingError('');
    if (!currentGradingQuestionId) return; // Should be set by modal context

    try {
      await teacherApi.gradeShortAnswer(attemptId, questionId, gradeScore, gradeFeedback);
      alert('批阅成功！');
      setGradeScore(0);
      setGradeFeedback('');
      setCurrentGradingQuestionId(null); // Reset
      await fetchAttemptDetail(attemptId); // Refresh attempt detail
    } catch (err) {
      setGradingError(err.detail || '批阅失败');
    }
  };

  const renderQuestionOptionsInput = () => {
    if (newQuestionType === 'single_choice' || newQuestionType === 'multiple_choice') {
      return (
        <div>
          <label>选项 (JSON格式, 例如: {"{\"A\":\"Option A\", \"B\":\"Option B\"}"}):</label>
          <textarea value={newQuestionOptions} onChange={(e) => setNewQuestionOptions(e.target.value)} rows="2"></textarea>
        </div>
      );
    }
    return null;
  };

  const renderCorrectAnswerInput = () => {
    if (newQuestionType === 'single_choice' || newQuestionType === 'fill_in_blank') {
      return (
        <div>
          <label>正确答案 (文本):</label>
          <input type="text" value={newQuestionCorrectAnswer} onChange={(e) => setNewQuestionCorrectAnswer(e.target.value)} />
        </div>
      );
    } else if (newQuestionType === 'multiple_choice') {
      return (
        <div>
          <label>正确答案 (JSON数组, 例如: {"[\"A\", \"C\"]"}):</label>
          <input type="text" value={newQuestionCorrectAnswer} onChange={(e) => setNewQuestionCorrectAnswer(e.target.value)} />
        </div>
      );
    }
    return null;
  };


  if (loading) return <div>Loading data...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>测验/作业管理</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新测验/作业</h3>
        <form onSubmit={handleCreateQuiz}>
          <div>
            <label>选择课程:</label>
            <select value={selectedCourseId} onChange={(e) => setSelectedCourseId(e.target.value)} required>
              <option value="">-- 请选择课程 --</option>
              {courses.map(course => (
                <option key={course.id} value={course.id}>{course.title}</option>
              ))}
            </select>
          </div>
          <div>
            <label>标题:</label>
            <input type="text" value={newQuizTitle} onChange={(e) => setNewQuizTitle(e.target.value)} required />
          </div>
          <div>
            <label>描述:</label>
            <textarea value={newQuizDesc} onChange={(e) => setNewQuizDesc(e.target.value)} rows="3"></textarea>
          </div>
          <div>
            <label>类型:</label>
            <select value={newQuizType} onChange={(e) => setNewQuizType(e.target.value)}>
              <option value="quiz">测验</option>
              <option value="assignment">作业</option>
            </select>
          </div>
          <div>
            <label>截止日期:</label>
            <input type="datetime-local" value={newQuizDueDate} onChange={(e) => setNewQuizDueDate(e.target.value)} />
          </div>
          <div>
            <label>
              <input type="checkbox" checked={newQuizIsPublished} onChange={(e) => setNewQuizIsPublished(e.target.checked)} />
              发布
            </label>
          </div>
          {createQuizError && <p className="error">{createQuizError}</p>}
          <button type="submit" disabled={!selectedCourseId || !newQuizTitle}>创建测验</button>
        </form>
      </div>

      <h3>测验/作业列表 ({selectedCourseId ? `课程ID: ${selectedCourseId}` : '请选择课程'})</h3>
      {!selectedCourseId ? (
        <p>请在上方选择一个课程来管理测验。</p>
      ) : quizzes.length === 0 ? (
        <p>此课程暂无测验或作业。</p>
      ) : (
        <ul>
          {quizzes.map((quiz) => (
            <li key={quiz.id} className="material-item">
              <span>
                {quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)}) - {quiz.is_published ? '已发布' : '草稿'}
                {quiz.due_date && ` - 截止: ${formatDateTime(quiz.due_date)}`}
              </span>
              <div>
                <button onClick={() => handleViewQuestions(quiz.id)}>管理问题</button>
                <button onClick={async () => {
                  try {
                    const attempts = await teacherApi.getAllAttemptsForQuiz(quiz.id);
                    alert(`此测验有 ${attempts.length} 次提交记录。`);
                    // 可以在这里导航到提交列表页或显示modal
                    // navigate(`/teacher/quizzes/${quiz.id}/attempts`);
                    handleViewAttempt(attempts[0]?.id); // View first attempt for simplicity
                  } catch (e) {
                    alert('获取提交记录失败: ' + (e.detail || e.message));
                  }
                }}>查看提交</button>
                <button onClick={() => alert('编辑功能待实现')}>编辑</button>
                <button onClick={() => handleDeleteQuiz(quiz.id)} style={{ backgroundColor: 'red' }}>删除</button>
              </div>
            </li>
          ))}
        </ul>
      )}

      {showQuestionsModal && (
        <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0,0,0,0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', width: '800px', maxHeight: '80%', overflowY: 'auto' }}>
            <h3>测验 {selectedQuizIdForQuestions} 的问题管理</h3>
            <form onSubmit={handleAddQuestion}>
              <h4>添加新问题</h4>
              <div>
                <label>问题文本:</label>
                <textarea value={newQuestionText} onChange={(e) => setNewQuestionText(e.target.value)} rows="3" required></textarea>
              </div>
              <div>
                <label>问题类型:</label>
                <select value={newQuestionType} onChange={(e) => setNewQuestionType(e.target.value)}>
                  <option value="single_choice">单选题</option>
                  <option value="multiple_choice">多选题</option>
                  <option value="fill_in_blank">填空题</option>
                  <option value="short_answer">问答题</option>
                </select>
              </div>
              {renderQuestionOptionsInput()}
              {renderCorrectAnswerInput()}
              <div>
                <label>分值:</label>
                <input type="number" value={newQuestionScore} onChange={(e) => setNewQuestionScore(e.target.value)} min="1" required />
              </div>
              {addQuestionError && <p className="error">{addQuestionError}</p>}
              <button type="submit">添加问题</button>
            </form>

            <h4 style={{ marginTop: '20px' }}>问题列表</h4>
            {questionsInQuiz.length === 0 ? (
              <p>此测验暂无问题。</p>
            ) : (
              <ul>
                {questionsInQuiz.map(q => (
                  <li key={q.id} className="material-item">
                    <span>
                      {getQuestionTypeDisplay(q.question_type)}: {q.question_text} (分值: {q.score})
                    </span>
                    <div>
                      {/* <button onClick={() => alert('编辑功能待实现')}>编辑</button> */}
                      <button onClick={() => handleDeleteQuestion(q.id)} style={{ backgroundColor: 'orange' }}>删除</button>
                    </div>
                  </li>
                ))}
              </ul>
            )}
            <button onClick={() => setShowQuestionsModal(false)}>关闭</button>
          </div>
        </div>
      )}

      {showAttemptModal && selectedAttemptDetail && (
        <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0,0,0,0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', width: '800px', maxHeight: '80%', overflowY: 'auto' }}>
            <h3>学生提交详情 (ID: {selectedAttemptId})</h3>
            <p>学生: {selectedAttemptDetail.student_username}</p>
            <p>测验: {selectedAttemptDetail.quiz_title}</p>
            <p>总得分: {selectedAttemptDetail.score !== null ? selectedAttemptDetail.score : '待批阅'}</p>
            <p>提交时间: {formatDateTime(selectedAttemptDetail.submit_time)}</p>

            <h4 style={{ marginTop: '20px' }}>问题作答</h4>
            {selectedAttemptDetail.graded_questions.map((q, idx) => (
              <div key={q.question_id} style={{ marginBottom: '15px', border: '1px solid #eee', padding: '10px', borderRadius: '5px', backgroundColor: q.is_correct === true ? '#e6ffe6' : q.is_correct === false ? '#ffe6e6' : 'inherit' }}>
                <h5>{idx + 1}. {q.question_text} (分值: {q.score})</h5>
                <p>类型: {getQuestionTypeDisplay(q.question_type)}</p>
                <p>学生回答: {JSON.stringify(q.submitted_answer)}</p>
                {q.question_type !== 'short_answer' && <p>正确答案: {JSON.stringify(q.correct_answer)}</p>}
                {q.is_correct !== null && <p>结果: {q.is_correct ? '✓ 正确' : '✗ 错误'}</p>}

                {q.question_type === 'short_answer' && (
                  <div style={{ marginTop: '10px' }}>
                    <strong>批阅:</strong>
                    <div>
                        <label>得分:</label>
                        <input type="number" value={currentGradingQuestionId === q.question_id ? gradeScore : q.score || 0} onChange={(e) => {
                            setGradeScore(parseFloat(e.target.value));
                            setCurrentGradingQuestionId(q.question_id); // Mark this question for grading
                        }} min="0" max={q.score} /> / {q.score}
                    </div>
                    <div>
                        <label>反馈:</label>
                        <textarea value={currentGradingQuestionId === q.question_id ? gradeFeedback : q.feedback || ''} onChange={(e) => {
                            setGradeFeedback(e.target.value);
                            setCurrentGradingQuestionId(q.question_id); // Mark this question for grading
                        }} rows="2"></textarea>
                    </div>
                    {gradingError && currentGradingQuestionId === q.question_id && <p className="error">{gradingError}</p>}
                    <button onClick={() => handleGradeShortAnswer(selectedAttemptId, q.question_id)} disabled={currentGradingQuestionId !== q.question_id}>保存批阅</button>
                  </div>
                )}
              </div>
            ))}
            <button onClick={() => setShowAttemptModal(false)}>关闭</button>
          </div>
        </div>
      )}
    </div>
  );
}

export default QuizManagement;