import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../api';

function UserProfile() {
    const { user, fetchCurrentUser } = useAuth();
    const [fullName, setFullName] = useState('');
    const [email, setEmail] = useState('');
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [updateError, setUpdateError] = useState('');
    const [passwordError, setPasswordError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (user) {
            setFullName(user.full_name || '');
            setEmail(user.email || '');
            setLoading(false);
        }
    }, [user]);

    const handleProfileUpdate = async (e) => {
        e.preventDefault();
        setUpdateError('');
        setSuccessMessage('');
        try {
            await authApi.updateProfile({ full_name: fullName, email: email });
            await fetchCurrentUser(); // 刷新用户数据
            setSuccessMessage('个人信息更新成功！');
        } catch (err) {
            setUpdateError(err.detail || '更新失败');
        }
    };

    const handleChangePassword = async (e) => {
        e.preventDefault();
        setPasswordError('');
        setSuccessMessage('');
        if (oldPassword === newPassword) {
            setPasswordError('新密码不能与旧密码相同。');
            return;
        }
        try {
            await authApi.changePassword(oldPassword, newPassword);
            setSuccessMessage('密码修改成功！');
            setOldPassword('');
            setNewPassword('');
        } catch (err) {
            setPasswordError(err.detail || '密码修改失败');
        }
    };

    if (loading) return <div>Loading profile...</div>;

    return (
        <div className="container">
            <h2>我的资料</h2>
            {successMessage && <p className="success">{successMessage}</p>}

            <div className="dashboard-card">
                <h3>基本信息</h3>
                <form onSubmit={handleProfileUpdate}>
                    <div>
                        <label>用户名:</label>
                        <input type="text" value={user?.username || ''} disabled />
                    </div>
                    <div>
                        <label>姓名:</label>
                        <input type="text" value={fullName} onChange={(e) => setFullName(e.target.value)} />
                    </div>
                    <div>
                        <label>邮箱:</label>
                        <input type="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                    </div>
                    {updateError && <p className="error">{updateError}</p>}
                    <button type="submit">更新信息</button>
                </form>
            </div>

            <div className="dashboard-card" style={{ marginTop: '20px' }}>
                <h3>修改密码</h3>
                <form onSubmit={handleChangePassword}>
                    <div>
                        <label>旧密码:</label>
                        <input type="password" value={oldPassword} onChange={(e) => setOldPassword(e.target.value)} required />
                    </div>
                    <div>
                        <label>新密码:</label>
                        <input type="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} required />
                    </div>
                    {passwordError && <p className="error">{passwordError}</p>}
                    <button type="submit">修改密码</button>
                </form>
            </div>
        </div>
    );
}

export default UserProfile;