import React, { useEffect, useState } from 'react';
import { studentApi } from '../api';
import { formatDateTime, getNotificationTypeDisplay } from '../utils';

function Notifications() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchNotifications = async () => {
    try {
      const data = await studentApi.getNotifications();
      setNotifications(data);
      setLoading(false);
    } catch (err) {
      setError(err.detail || 'Failed to fetch notifications');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const handleMarkRead = async (notificationId) => {
    try {
      await studentApi.markNotificationRead(notificationId);
      // 刷新列表以显示更新后的状态
      await fetchNotifications();
      alert('通知已标记为已读！');
    } catch (err) {
      alert('标记失败: ' + (err.detail || err.message));
    }
  };

  if (loading) return <div>Loading notifications...</div>;
  if (error) return <div className="error">{error}</div>;
  if (notifications.length === 0) return <div>没有通知。</div>;

  return (
    <div className="container">
      <h2>我的通知</h2>
      <ul>
        {notifications.map((notification) => (
          <li key={notification.id} className={`notification-item ${notification.is_read ? 'read' : ''}`}>
            <div>
              <strong>{notification.title}</strong>
              <p>{notification.content}</p>
              <small>{getNotificationTypeDisplay(notification.type)} - {formatDateTime(notification.created_at)}</small>
            </div>
            {!notification.is_read && (
              <button onClick={() => handleMarkRead(notification.id)}>标记已读</button>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default Notifications;