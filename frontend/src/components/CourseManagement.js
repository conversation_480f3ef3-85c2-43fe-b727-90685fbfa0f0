import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { teacherApi, materialApi } from '../api';
import { formatDateTime, getFileTypeIcon } from '../utils';

function CourseManagement() {
  const [courses, setCourses] = useState([]);
  const [newCourseTitle, setNewCourseTitle] = useState('');
  const [newCourseDesc, setNewCourseDesc] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createError, setCreateError] = useState('');

  // States for selected course for material upload
  const [selectedCourseId, setSelectedCourseId] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [uploadSuccess, setUploadSuccess] = useState('');

  const fetchCourses = async () => {
    try {
      const data = await teacherApi.getCourses();
      setCourses(data);
      setLoading(false);
    } catch (err) {
      setError(err.detail || 'Failed to fetch courses');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  const handleCreateCourse = async (e) => {
    e.preventDefault();
    setCreateError('');
    try {
      await teacherApi.createCourse({ title: newCourseTitle, description: newCourseDesc });
      setNewCourseTitle('');
      setNewCourseDesc('');
      await fetchCourses(); // Refresh list
      alert('课程创建成功！');
    } catch (err) {
      setCreateError(err.detail || '创建课程失败');
    }
  };

  const handleDeleteCourse = async (courseId) => {
    if (window.confirm('确定删除此课程及其所有内容吗？此操作不可逆！')) {
      try {
        await teacherApi.deleteCourse(courseId);
        await fetchCourses(); // Refresh list
        alert('课程删除成功！');
      } catch (err) {
        alert('删除课程失败: ' + (err.detail || err.message));
      }
    }
  };

  // Material Upload Logic
  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
    setUploadError('');
    setUploadSuccess('');
  };

  const handleMaterialUpload = async (e) => {
    e.preventDefault();
    if (!selectedFile || !selectedCourseId) {
      setUploadError('请选择文件和课程');
      return;
    }

    setUploading(true);
    setUploadError('');
    setUploadSuccess('');

    try {
      const materialType = mapMimeToMaterialType(selectedFile.type);
      const { upload_url, object_name } = await materialApi.getUploadCredentials(
        selectedCourseId,
        selectedFile.name,
        materialType
      );

      // 直接上传到 Minio
      await materialApi.uploadFileToMinio(upload_url, selectedFile, selectedFile.type); // file.type 是完整的MIME类型

      // 通知后端创建资料记录
      const materialData = {
        title: selectedFile.name.split('.')[0],
        description: `Uploaded from ${selectedFile.name}`,
        file_type: materialType,
        file_path_minio: object_name,
        file_size_bytes: selectedFile.size,
        // duration_seconds: getDurationOfMedia(selectedFile), // For video/audio, requires more complex client-side logic
      };
      await materialApi.createMaterialRecord(selectedCourseId, materialData);

      setUploadSuccess('文件上传并记录成功！');
      setSelectedFile(null);
      setSelectedCourseId(null);
      // 可以选择刷新课程资料列表，这里暂不实现
    } catch (err) {
      setUploadError(err.detail || '文件上传失败');
      console.error(err);
    } finally {
      setUploading(false);
    }
  };


  if (loading) return <div>Loading courses...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>课程管理</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新课程</h3>
        <form onSubmit={handleCreateCourse}>
          <div>
            <label>课程标题:</label>
            <input type="text" value={newCourseTitle} onChange={(e) => setNewCourseTitle(e.target.value)} required />
          </div>
          <div>
            <label>描述:</label>
            <textarea value={newCourseDesc} onChange={(e) => setNewCourseDesc(e.target.value)} rows="3"></textarea>
          </div>
          {createError && <p className="error">{createError}</p>}
          <button type="submit" disabled={!newCourseTitle}>创建课程</button>
        </form>
      </div>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>上传教学资料</h3>
        <form onSubmit={handleMaterialUpload}>
          <div>
            <label>选择课程:</label>
            <select value={selectedCourseId || ''} onChange={(e) => setSelectedCourseId(e.target.value)} required>
              <option value="">-- 请选择课程 --</option>
              {courses.map(course => (
                <option key={course.id} value={course.id}>{course.title}</option>
              ))}
            </select>
          </div>
          <div>
            <label>选择文件:</label>
            <input type="file" onChange={handleFileChange} required />
          </div>
          {uploadError && <p className="error">{uploadError}</p>}
          {uploadSuccess && <p className="success">{uploadSuccess}</p>}
          <button type="submit" disabled={!selectedFile || !selectedCourseId || uploading}>
            {uploading ? '上传中...' : '上传资料'}
          </button>
        </form>
      </div>

      <h3>我的课程列表</h3>
      {courses.length === 0 ? (
        <p>您还没有创建任何课程。</p>
      ) : (
        <ul>
          {courses.map((course) => (
            <li key={course.id} className="material-item">
              <span>
                <Link to={`/teacher/quizzes/${course.id}`}>{course.title}</Link> ({course.status})
              </span>
              <div>
                <button onClick={() => alert('编辑功能待实现')}>编辑</button>
                <button onClick={() => handleDeleteCourse(course.id)} style={{ backgroundColor: 'red' }}>删除</button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default CourseManagement;


// Helper to map MIME type to backend MaterialType enum
const mapMimeToMaterialType = (mimeType) => {
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('powerpoint')) return 'ppt';
    if (mimeType.includes('pdf')) return 'pdf';
    if (mimeType.includes('word')) return 'word';
    if (mimeType.startsWith('audio/')) return 'audio';
    if (mimeType.startsWith('text/')) return 'text';
    return 'text'; // Default fallback
};