import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

function TeacherDashboard() {
  const { user } = useAuth();
  return (
    <div className="container">
      <h2>教师管理面板 - 欢迎, {user?.full_name || user?.username}!</h2>
      <div className="dashboard-card">
        <h3>管理中心</h3>
        <ul>
          <li><Link to="/teacher/courses">课程管理</Link></li>
          <li><Link to="/teacher/classes">班级管理</Link></li>
          {/* 测验管理将通过课程进入，或直接通过 /teacher/quizzes */}
          <li><Link to="/teacher/quizzes">测验/作业管理</Link></li>
          <li><Link to="/teacher/reports">学习报告</Link></li>
          {/* <li><Link to="/teacher/materials">资料管理 (已包含在课程中)</Link></li> */}
        </ul>
      </div>
      {/* 可以在这里添加一些教师专属的统计概览 */}
    </div>
  );
}

export default TeacherDashboard;