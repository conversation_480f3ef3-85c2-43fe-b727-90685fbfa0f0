import React, { useEffect, useState } from 'react';
import { teacherApi, authApi } from '../api';
import { formatDateTime } from '../utils';

function ClassManagement() {
  const [classes, setClasses] = useState([]);
  const [newClassName, setNewClassName] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createError, setCreateError] = useState('');

  const [selectedClassId, setSelectedClassId] = useState(null);
  const [studentsToAdd, setStudentsToAdd] = useState(''); // Comma-separated student IDs
  const [addStudentError, setAddStudentError] = useState('');
  const [addStudentSuccess, setAddStudentSuccess] = useState('');
  const [studentsInSelectedClass, setStudentsInSelectedClass] = useState([]);
  const [showStudentsModal, setShowStudentsModal] = useState(false);

  const fetchClasses = async () => {
    try {
      const data = await teacherApi.getClasses();
      setClasses(data);
      setLoading(false);
    } catch (err) {
      setError(err.detail || 'Failed to fetch classes');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const handleCreateClass = async (e) => {
    e.preventDefault();
    setCreateError('');
    try {
      await teacherApi.createClass({ name: newClassName });
      setNewClassName('');
      await fetchClasses();
      alert('班级创建成功！');
    } catch (err) {
      setCreateError(err.detail || '创建班级失败');
    }
  };

  const handleDeleteClass = async (classId) => {
    if (window.confirm('确定删除此班级及其所有学生关联吗？此操作不可逆！')) {
      try {
        await teacherApi.deleteClass(classId);
        await fetchClasses();
        alert('班级删除成功！');
      } catch (err) {
        alert('删除班级失败: ' + (err.detail || err.message));
      }
    }
  };

  const handleAddStudents = async (e) => {
    e.preventDefault();
    setAddStudentError('');
    setAddStudentSuccess('');
    if (!selectedClassId || !studentsToAdd.trim()) {
      setAddStudentError('请选择班级并输入学生ID。');
      return;
    }

    const studentIds = studentsToAdd.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    if (studentIds.length === 0) {
        setAddStudentError('请输入有效的学生ID列表 (逗号分隔)。');
        return;
    }

    try {
      await teacherApi.addStudentsToClass(selectedClassId, studentIds);
      setAddStudentSuccess('学生添加成功！');
      setStudentsToAdd('');
      await fetchClasses(); // Refresh class list to update student count
      if (showStudentsModal) { // If modal is open, refresh student list in modal
        await fetchStudentsInClass(selectedClassId);
      }
    } catch (err) {
      setAddStudentError(err.detail || '添加学生失败');
    }
  };

  const fetchStudentsInClass = async (classId) => {
    try {
      const students = await teacherApi.getStudentsInClass(classId);
      setStudentsInSelectedClass(students);
    } catch (err) {
      setAddStudentError(err.detail || '无法获取班级学生列表');
    }
  };

  const handleViewStudents = async (classId) => {
    setSelectedClassId(classId);
    await fetchStudentsInClass(classId);
    setShowStudentsModal(true);
  };

  const handleRemoveStudent = async (classId, studentId) => {
    if (window.confirm('确定将此学生从班级中移除吗？')) {
      try {
        await teacherApi.removeStudentFromClass(classId, studentId);
        alert('学生移除成功！');
        await fetchStudentsInClass(classId); // Refresh student list in modal
        await fetchClasses(); // Refresh class list to update student count
      } catch (err) {
        alert('移除学生失败: ' + (err.detail || err.message));
      }
    }
  };


  if (loading) return <div>Loading classes...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <div className="container">
      <h2>班级管理</h2>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>创建新班级</h3>
        <form onSubmit={handleCreateClass}>
          <div>
            <label>班级名称:</label>
            <input type="text" value={newClassName} onChange={(e) => setNewClassName(e.target.value)} required />
          </div>
          {createError && <p className="error">{createError}</p>}
          <button type="submit" disabled={!newClassName}>创建班级</button>
        </form>
      </div>

      <div className="dashboard-card" style={{ marginBottom: '20px' }}>
        <h3>添加学生到班级</h3>
        <form onSubmit={handleAddStudents}>
          <div>
            <label>选择班级:</label>
            <select value={selectedClassId || ''} onChange={(e) => setSelectedClassId(e.target.value)} required>
              <option value="">-- 请选择班级 --</option>
              {classes.map(cls => (
                <option key={cls.id} value={cls.id}>{cls.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label>学生ID (逗号分隔):</label>
            <input type="text" value={studentsToAdd} onChange={(e) => setStudentsToAdd(e.target.value)} placeholder="例如: 101,102,103" required />
          </div>
          {addStudentError && <p className="error">{addStudentError}</p>}
          {addStudentSuccess && <p className="success">{addStudentSuccess}</p>}
          <button type="submit" disabled={!selectedClassId || !studentsToAdd.trim()}>添加学生</button>
        </form>
      </div>

      <h3>我的班级列表</h3>
      {classes.length === 0 ? (
        <p>您还没有创建任何班级。</p>
      ) : (
        <ul>
          {classes.map((cls) => (
            <li key={cls.id} className="material-item">
              <span>
                {cls.name} (学生人数: {cls.student_count}) - {cls.teacher_name}
              </span>
              <div>
                <button onClick={() => handleViewStudents(cls.id)}>查看学生</button>
                <button onClick={() => alert('编辑功能待实现')}>编辑</button>
                <button onClick={() => handleDeleteClass(cls.id)} style={{ backgroundColor: 'red' }}>删除</button>
              </div>
            </li>
          ))}
        </ul>
      )}

      {showStudentsModal && (
        <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(0,0,0,0.5)', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', width: '600px', maxHeight: '80%', overflowY: 'auto' }}>
            <h3>班级 {selectedClassId} 学生列表</h3>
            {studentsInSelectedClass.length === 0 ? (
              <p>班级中没有学生。</p>
            ) : (
              <ul>
                {studentsInSelectedClass.map(student => (
                  <li key={student.id} className="material-item">
                    <span>{student.full_name || student.username} (ID: {student.id}) - {student.email}</span>
                    <button onClick={() => handleRemoveStudent(selectedClassId, student.id)} style={{ backgroundColor: 'orange' }}>移除</button>
                  </li>
                ))}
              </ul>
            )}
            <button onClick={() => setShowStudentsModal(false)}>关闭</button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ClassManagement;