import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { studentApi } from '../api';
import { formatDateTime, getQuizTypeDisplay, getQuestionTypeDisplay } from '../utils';

function QuizResult() {
  const { attemptId } = useParams();
  const [attemptDetail, setAttemptDetail] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchAttemptDetail = async () => {
      try {
        const data = await studentApi.getQuizAttemptDetail(attemptId);
        setAttemptDetail(data);
        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch quiz result');
        setLoading(false);
      }
    };

    fetchAttemptDetail();
  }, [attemptId]);

  if (loading) return <div>Loading quiz result...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!attemptDetail) return <div>Quiz result not found.</div>;

  return (
    <div className="container">
      <h2>{attemptDetail.quiz_title} 结果</h2>
      <p>得分: {attemptDetail.score !== null ? attemptDetail.score : '待批阅'}</p>
      <p>提交时间: {formatDateTime(attemptDetail.submit_time)}</p>

      <h3>我的答案</h3>
      {attemptDetail.questions_answered.length === 0 ? (
        <p>无问题记录。</p>
      ) : (
        attemptDetail.questions_answered.map((question, index) => (
          <div key={question.id} style={{ marginBottom: '20px', border: '1px solid #ddd', padding: '15px', borderRadius: '8px', backgroundColor: question.is_correct === true ? '#e6ffe6' : question.is_correct === false ? '#ffe6e6' : 'inherit' }}>
            <h4>{index + 1}. {question.question_text} (分值: {question.score})</h4>
            <p>类型: {getQuestionTypeDisplay(question.question_type)}</p>

            {question.question_type === 'single_choice' && (
                <>
                    <p>你的选择: {question.submitted_answer}</p>
                    {question.options && <p>选项: {Object.entries(question.options).map(([key, value]) => `${key}. ${value}`).join(', ')}</p>}
                </>
            )}
            {question.question_type === 'multiple_choice' && (
                <>
                    <p>你的选择: {(question.submitted_answer || []).join(', ')}</p>
                    {question.options && <p>选项: {Object.entries(question.options).map(([key, value]) => `${key}. ${value}`).join(', ')}</p>}
                </>
            )}
            {question.question_type === 'fill_in_blank' && (
                <p>你的回答: {question.submitted_answer}</p>
            )}
            {question.question_type === 'short_answer' && (
                <p>你的回答:<br/>{question.submitted_answer}</p>
            )}

            {question.is_correct !== null && (
                <p>结果: {question.is_correct ? <span style={{color: 'green'}}>✓ 正确</span> : <span style={{color: 'red'}}>✗ 错误</span>}</p>
            )}
          </div>
        ))
      )}
    </div>
  );
}

export default QuizResult;
