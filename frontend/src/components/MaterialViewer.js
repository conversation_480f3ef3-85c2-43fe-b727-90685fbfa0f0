import React, { useEffect, useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { studentApi, materialApi } from '../api';
import { formatDuration, getFileTypeIcon } from '../utils';

function MaterialViewer() {
  const { materialId } = useParams();
  const [material, setMaterial] = useState(null);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const videoRef = useRef(null);
  const lastReportedTime = useRef(0); // 用于记录上次上报的时间点

  useEffect(() => {
    const fetchMaterial = async () => {
      try {
        const materialData = await studentApi.getMaterialDetailForStudent(materialId);
        setMaterial(materialData);

        const urlData = await materialApi.getDownloadUrl(materialId);
        setDownloadUrl(urlData.download_url);
        setLoading(false);

        // 如果是视频或音频，初始化上次观看时间
        if (materialData.file_type === 'video' || materialData.file_type === 'audio') {
            lastReportedTime.current = materialData.my_last_viewed_timestamp || 0;
            if (videoRef.current && materialData.my_last_viewed_timestamp) {
                videoRef.current.currentTime = materialData.my_last_viewed_timestamp;
            }
        }

      } catch (err) {
        setError(err.detail || 'Failed to fetch material details');
        setLoading(false);
      }
    };

    fetchMaterial();
  }, [materialId]);

  const handleProgressUpdate = async (type) => {
    if (!material) return;

    let progressData = {
        progress_percentage: material.my_progress_percentage,
        last_viewed_timestamp: material.my_last_viewed_timestamp,
        view_duration_increment: 0,
        is_completed: material.my_is_completed,
    };

    if (type === 'video' && videoRef.current) {
        const video = videoRef.current;
        const currentTime = video.currentTime;
        const duration = video.duration;

        progressData.last_viewed_timestamp = Math.round(currentTime);
        progressData.progress_percentage = parseFloat(((currentTime / duration) * 100).toFixed(2));
        progressData.view_duration_increment = Math.round(currentTime - lastReportedTime.current);
        progressData.is_completed = currentTime >= duration - 1; // 视频播放到最后1秒视为完成

        lastReportedTime.current = currentTime; // 更新上报时间点
    } else if (type === 'document') {
        // 对于文档，可以根据滚动位置或页码来计算进度
        // 简化：假设用户点击完成按钮即完成
        progressData.is_completed = true;
        progressData.progress_percentage = 100.0;
        progressData.last_viewed_timestamp = 0; // 或者记录当前页码
        progressData.view_duration_increment = 0; // 无法自动获取阅读时长，可通过按钮事件触发
    }

    try {
        const updatedProgress = await studentApi.updateMaterialProgress(material.id, progressData);
        setMaterial(prev => ({
            ...prev,
            my_progress_percentage: updatedProgress.progress_percentage,
            my_last_viewed_timestamp: updatedProgress.last_viewed_timestamp,
            my_total_view_duration_seconds: updatedProgress.view_duration_increment, // 注意这里是总时长
            my_is_completed: updatedProgress.is_completed,
        }));
        // console.log('Progress updated successfully:', updatedProgress);
    } catch (err) {
        console.error('Failed to update progress:', err.detail || err.message);
    }
  };

  if (loading) return <div>Loading material...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!material) return <div>Material not found.</div>;

  const renderMaterialContent = () => {
    if (!downloadUrl) {
      return <p>无法加载资料内容，下载链接缺失。</p>;
    }

    switch (material.file_type) {
      case 'video':
        return (
          <video
            ref={videoRef}
            src={downloadUrl}
            controls
            autoPlay
            style={{ width: '100%', maxWidth: '800px' }}
            onTimeUpdate={() => handleProgressUpdate('video')}
            onEnded={() => handleProgressUpdate('video')}
          >
            Your browser does not support the video tag.
          </video>
        );
      case 'audio':
        return (
          <audio
            ref={videoRef} // Reuse videoRef for audio
            src={downloadUrl}
            controls
            autoPlay
            style={{ width: '100%', maxWidth: '800px' }}
            onTimeUpdate={() => handleProgressUpdate('video')}
            onEnded={() => handleProgressUpdate('video')}
          >
            Your browser does not support the audio tag.
          </audio>
        );
      case 'image':
        return <img src={downloadUrl} alt={material.title} style={{ maxWidth: '100%' }} />;
      case 'pdf':
      case 'ppt':
      case 'word':
      case 'text':
        return (
          <div>
            <p>文件类型: {material.file_type} {getFileTypeIcon(material.file_type)}</p>
            <p><strong>无法直接在浏览器中预览，请点击下载查看。</strong></p>
            <a href={downloadUrl} target="_blank" rel="noopener noreferrer">下载 {material.title}</a>
            <button onClick={() => handleProgressUpdate('document')} disabled={material.my_is_completed}>标记为已完成</button>
          </div>
        );
      default:
        return <p>不支持的文件类型: {material.file_type}</p>;
    }
  };

  return (
    <div className="container">
      <h2>{material.title}</h2>
      <p>描述: {material.description}</p>
      <p>学习进度: {material.my_progress_percentage}% 完成</p>
      <p>总学习时长: {formatDuration(material.my_total_view_duration_seconds)}</p>
      <p>上次观看: {material.my_last_viewed_timestamp ? `${material.my_last_viewed_timestamp}s` : '从未'}</p>
      <p>已完成: {material.my_is_completed ? '是' : '否'}</p>

      <div style={{ marginTop: '20px', border: '1px solid #eee', padding: '15px' }}>
        {renderMaterialContent()}
      </div>
    </div>
  );
}

export default MaterialViewer;