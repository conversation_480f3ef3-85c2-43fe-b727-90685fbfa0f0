import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { studentApi } from '../api';
import { formatDateTime, formatDuration, getQuizTypeDisplay } from '../utils';
import { useAuth } from '../contexts/AuthContext';
import { websocketService } from '../api'; // 导入 WebSocket 服务

function StudentDashboard() {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { user, isAuthenticated, userRole, logout } = useAuth();

  useEffect(() => {
    if (!isAuthenticated || userRole !== 'student') {
      return;
    }

    const fetchDashboardData = async () => {
      try {
        const data = await studentApi.getDashboardSummary();
        setDashboardData(data);
        setLoading(false);
      } catch (err) {
        setError(err.detail || 'Failed to fetch dashboard data');
        setLoading(false);
      }
    };

    fetchDashboardData();

    // 设置 WebSocket 连接以接收实时通知
    let ws = null;
    if (user && user.id) { // Ensure user is loaded
      ws = websocketService.connect(localStorage.getItem('jwtToken'), (notification) => {
        console.log('Real-time notification received:', notification);
        alert(`新通知: ${notification.title}\n内容: ${notification.content}`);
        // 收到通知后，可以更新dashboardData或重新获取通知列表
        fetchDashboardData();
      });
    }

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [isAuthenticated, user, userRole]);

  if (loading) return <div>Loading dashboard...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!dashboardData) return <div>No dashboard data available.</div>;

  return (
    <div className="container">
      <h2>学生仪表盘 - 欢迎, {user?.full_name || user?.username}!</h2>

      <div className="dashboard-card">
        <h3>我的课程</h3>
        {dashboardData.my_courses.length === 0 ? (
          <p>您还没有加入任何课程。</p>
        ) : (
          <ul>
            {dashboardData.my_courses.map((course) => (
              <li key={course.id} className="material-item">
                <Link to={`/student/courses/${course.id}`}>
                  {course.title} ({course.my_progress_percentage}% 完成)
                </Link>
                <progress value={course.my_progress_percentage} max="100"></progress>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="dashboard-card">
        <h3>即将到来的测验/作业</h3>
        {dashboardData.upcoming_quizzes.length === 0 ? (
          <p>近期没有即将到来的测验或作业。</p>
        ) : (
          <ul>
            {dashboardData.upcoming_quizzes.map((quiz) => (
              <li key={quiz.id} className="quiz-item">
                {quiz.is_completed_by_me ? (
                  <span>{quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)}) - 已完成 (得分: {quiz.my_score})</span>
                ) : (
                  <Link to={`/student/quizzes/${quiz.id}`}>
                    {quiz.title} ({getQuizTypeDisplay(quiz.quiz_type)}) - 截止日期: {formatDateTime(quiz.due_date)}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="dashboard-card">
        <h3>近期通知</h3>
        {dashboardData.recent_notifications.length === 0 ? (
          <p>您近期没有新的通知。</p>
        ) : (
          <ul>
            {dashboardData.recent_notifications.map((notification) => (
              <li key={notification.id} className={`notification-item ${notification.is_read ? 'read' : ''}`}>
                <span>{notification.title} ({formatDateTime(notification.created_at)})</span>
                {!notification.is_read && (
                    <button onClick={async () => {
                        try {
                            await studentApi.markNotificationRead(notification.id);
                            // 标记成功后更新UI或重新获取dashboardData
                            alert('通知已标记为已读！');
                            fetchDashboardData();
                        } catch (e) {
                            alert('标记失败: ' + (e.detail || e.message));
                        }
                    }}>标记已读</button>
                )}
              </li>
            ))}
          </ul>
        )}
        <Link to="/notifications">查看所有通知</Link>
      </div>
    </div>
  );
}

export default StudentDashboard;