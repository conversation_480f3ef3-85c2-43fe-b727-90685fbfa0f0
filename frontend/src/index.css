body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
  color: #333;
}

#root {
  max-width: 1200px;
  margin: 20px auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
  padding: 20px;
}

h1, h2, h3 {
  color: #0056b3;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
textarea,
select {
  width: calc(100% - 20px);
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
}

button:hover {
  background-color: #0056b3;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.error {
  color: red;
  margin-bottom: 10px;
}

.success {
  color: green;
  margin-bottom: 10px;
}

.dashboard-card {
    background-color: #e9f5ff;
    border: 1px solid #cce5ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.material-item, .quiz-item {
    background-color: #f8f9fa;
    border: 1px solid #e2e6ea;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.material-item button, .quiz-item button {
    padding: 5px 10px;
    font-size: 0.8em;
}

.tabs button {
    background-color: #f0f0f0;
    color: #333;
    border-bottom: 2px solid transparent;
}

.tabs button.active {
    background-color: #007bff;
    color: white;
    border-bottom: 2px solid #0056b3;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #007bff;
  padding: 10px 20px;
  color: white;
  border-radius: 8px;
  margin-bottom: 20px;
}

nav a {
  color: white;
  text-decoration: none;
  margin-left: 20px;
}

nav a:hover {
  text-decoration: underline;
}

.notification-item {
  background-color: #ffeccf;
  border: 1px solid #ffd700;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.notification-item.read {
  background-color: #e0e0e0;
  color: #666;
}