from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from app.models import UserRole, MaterialType, QuizType, QuestionType, NotificationType
import json


# --- Token and User related Schemas ---

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    username: Optional[str] = None
    user_id: Optional[int] = None
    role: Optional[UserRole] = None


class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50, pattern=r"^[a-zA-Z0-9_.-]+$")
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=2, max_length=100)


class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=64)
    role: UserRole = UserRole.student


class UserUpdate(BaseModel):
    full_name: Optional[str] = Field(None, min_length=2, max_length=100)
    avatar_url: Optional[str] = None


class UserAdminUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50, pattern=r"^[a-zA-Z0-9_.-]+$")
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=2, max_length=100)
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    password: Optional[str] = Field(None, min_length=8, max_length=64)


class UserInDBBase(UserBase):
    id: int
    role: UserRole
    is_active: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None
    avatar_url: Optional[str] = None

    class Config:
        from_attributes = True


class UserPublic(UserInDBBase):
    pass


class UserLogin(BaseModel):
    username: str
    password: str


class PasswordChange(BaseModel):
    old_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=64)


# --- Course related Schemas ---

class CourseBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    cover_image_url: Optional[str] = Field(None, max_length=255)


class CourseCreate(CourseBase):
    pass


class CourseUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    cover_image_url: Optional[str] = Field(None, max_length=255)
    status: Optional[str] = Field(None, pattern=r"^(draft|published|archived)$")


class CourseInDB(CourseBase):
    id: int
    teacher_id: int
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Teacher view of course summary
class CourseSummaryForTeacher(CourseInDB):
    teacher_name: str


# Student view of course summary
class CourseSummaryForStudent(CourseInDB):
    my_progress_percentage: Optional[float] = 0.0


# --- Material related Schemas ---

class MaterialBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    file_type: MaterialType


class MaterialCreate(MaterialBase):
    file_path_minio: str = Field(..., max_length=255)
    file_size_bytes: Optional[int] = Field(None, ge=0)
    duration_seconds: Optional[int] = Field(None, ge=0)
    preview_url: Optional[str] = Field(None, max_length=255)


class MaterialUpdate(MaterialBase):  # Reusing MaterialBase fields for update
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    file_type: Optional[MaterialType] = None
    file_size_bytes: Optional[int] = Field(None, ge=0)
    duration_seconds: Optional[int] = Field(None, ge=0)
    preview_url: Optional[str] = Field(None, max_length=255)


class MaterialInDB(MaterialBase):
    id: int
    course_id: int
    file_path_minio: str
    file_size_bytes: Optional[int] = None
    duration_seconds: Optional[int] = None
    milvus_vector_id: Optional[str] = None
    created_by_user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PresignedUploadUrlResponse(BaseModel):
    upload_url: str
    object_name: str
    expires_in_seconds: int


class PresignedDownloadUrlResponse(BaseModel):
    download_url: str
    expires_in_seconds: int


class MaterialUploadedConfirmation(MaterialBase):
    file_path_minio: str = Field(..., max_length=255)
    file_size_bytes: Optional[int] = None
    duration_seconds: Optional[int] = None
    preview_url: Optional[str] = None


# For student view of material, includes progress
class MaterialInCourseForStudent(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    file_type: MaterialType
    file_path_minio: str  # Internal use, not meant for frontend direct access
    preview_url: Optional[str] = None
    duration_seconds: Optional[int] = None

    my_progress_percentage: Optional[float] = 0.0
    my_last_viewed_timestamp: Optional[int] = None
    my_total_view_duration_seconds: Optional[int] = 0
    my_is_completed: bool = False

    class Config:
        from_attributes = True


# Student material progress update schema
class StudentMaterialProgressUpdate(BaseModel):
    progress_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    last_viewed_timestamp: Optional[int] = None
    total_view_duration_seconds: Optional[int] = Field(None, ge=0)
    is_completed: Optional[bool] = None


# --- Class related Schemas ---

class ClassBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)


class ClassCreate(ClassBase):
    pass


class ClassUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)


class ClassInDB(ClassBase):
    id: int
    teacher_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ClassSummaryForTeacher(ClassInDB):
    teacher_name: str
    student_count: int = 0


class AddStudentsToClassRequest(BaseModel):
    student_ids: List[int] = Field(..., min_items=1)


class StudentInClass(BaseModel):
    id: int
    username: str
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    joined_at: datetime

    class Config:
        from_attributes = True


# --- Quiz (Test/Assignment) related Schemas ---

class QuizBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    quiz_type: QuizType
    due_date: Optional[datetime] = None
    is_published: bool = False


class QuizCreate(QuizBase):
    course_id: int = Field(..., gt=0)


class QuizUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    quiz_type: Optional[QuizType] = None
    due_date: Optional[datetime] = None
    is_published: Optional[bool] = None


class QuizInDB(QuizBase):
    id: int
    course_id: int
    created_by_user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Student view of quiz summary
class QuizSummaryForStudent(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    quiz_type: QuizType
    due_date: Optional[datetime] = None
    is_published: bool
    is_completed_by_me: bool = False
    my_score: Optional[float] = None

    class Config:
        from_attributes = True


# --- QuizQuestion related Schemas ---

class QuestionBase(BaseModel):
    question_text: str = Field(..., min_length=1, max_length=1000)
    question_type: QuestionType
    options: Optional[Dict[str, str]] = None
    correct_answer: Optional[Union[str, List[str]]] = None
    score: int = Field(1, gt=0, le=100)
    question_order: Optional[int] = Field(None, ge=0)

    @validator('options', pre=True, always=True)
    def parse_options(cls, v):
        if isinstance(v, str) and v.strip():  # Check for non-empty string
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                raise ValueError("Options must be a valid JSON string or dictionary")
        return v

    @validator('correct_answer', pre=True, always=True)
    def parse_correct_answer(cls, v):
        if isinstance(v, str) and v.strip() and (v.startswith('[') or v.startswith('{') or v.startswith(
                '"')):  # Check for non-empty string and potential JSON
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # If it's not valid JSON, but still a string, keep it as is (for fill-in-blank)
                return v
        return v


class QuestionCreate(QuestionBase):
    pass


class QuestionUpdate(QuestionBase):
    pass


class QuestionInDB(QuestionBase):
    id: int
    quiz_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Question for student view (without correct_answer)
class QuestionForStudent(BaseModel):
    id: int
    question_text: str
    question_type: QuestionType
    options: Optional[Dict[str, str]] = None
    question_order: Optional[int] = None
    score: int

    class Config:
        from_attributes = True


# --- StudentQuizAttempt related Schemas ---

class SubmittedAnswer(BaseModel):
    question_id: int = Field(..., gt=0)
    answer: Any  # Flexible to accept str, list, etc.


class StudentAttemptCreate(BaseModel):
    submitted_answers: List[SubmittedAnswer] = Field(..., min_items=1)


class StudentAttemptInDB(BaseModel):
    id: int
    student_id: int
    quiz_id: int
    score: Optional[float] = None
    submitted_answers: Dict[str, Any]  # Will be parsed from JSON string by ORM or manually
    start_time: Optional[datetime] = None
    submit_time: Optional[datetime] = None
    is_completed: bool = False
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        
                                            ensure_ascii=False)  # Ensure JSON fields are correctly serialized


# Teacher grading request
class GradeShortAnswerRequest(BaseModel):
    score: float = Field(..., ge=0, le=100)
    feedback: Optional[str] = Field(None, max_length=500)


# Teacher view of a single graded question
class GradedQuestion(BaseModel):
    question_id: int
    question_text: str
    question_type: QuestionType
    submitted_answer: Optional[Any] = None  # Can be dict, str, list
    correct_answer: Optional[Any] = None  # Can be dict, str, list
    score: Optional[float] = None
    feedback: Optional[str] = None
    is_correct: Optional[bool] = None


# Teacher view of student attempt detail
class StudentAttemptDetailForTeacher(StudentAttemptInDB):
    student_username: str
    quiz_title: str
    graded_questions: List[GradedQuestion] = []

    class Config:
        from_attributes = True


# Student view of attempt detail
class StudentAttemptDetailForStudent(StudentAttemptInDB):
    quiz_title: str
    questions_answered: List[
        QuestionForStudent] = []  # Note: QuestionForStudent needs to handle submitted_answer and is_correct

    class Config:
        from_attributes = True


# Temporary schema for QuestionForStudent to carry attempt info
class QuestionForStudentWithAttemptInfo(QuestionForStudent):
    submitted_answer: Optional[Any] = None
    is_correct: Optional[bool] = None


# --- Notification Schemas ---

class NotificationCreate(BaseModel):
    target_user_id: Optional[int] = None
    target_class_id: Optional[int] = None
    target_course_id: Optional[int] = None
    type: NotificationType
    title: str = Field(..., min_length=1, max_length=255)
    content: Optional[str] = Field(None, max_length=1000)
    is_read: bool = False


class NotificationInDB(BaseModel):
    id: int
    target_user_id: Optional[int] = None
    target_class_id: Optional[int] = None
    target_course_id: Optional[int] = None
    type: NotificationType
    title: str
    content: Optional[str] = None
    is_read: bool
    created_at: datetime

    class Config:
        from_attributes = True


# --- Learning Report Schemas ---

class DailyStudyDuration(BaseModel):
    date: datetime
    duration_seconds: int


class MaterialCompletionRate(BaseModel):
    material_id: int
    material_title: str
    completion_percentage: float
    completed_students_count: int


class QuizScoreDistribution(BaseModel):
    score_range: str
    student_count: int
    percentage: float


class StudentLearningOverview(BaseModel):
    student_id: int
    student_name: str
    total_courses_enrolled: int
    completed_courses_count: int
    average_course_progress: float
    total_study_duration_seconds: int
    average_quiz_score: Optional[float] = None


class CourseLearningReport(BaseModel):
    course_id: int
    course_title: str
    total_students: int
    average_completion_percentage: float
    material_completion_rates: List[MaterialCompletionRate]
    quiz_score_distribution: List[QuizScoreDistribution]


class ClassLearningReport(BaseModel):
    class_id: int
    class_name: str
    teacher_name: str
    total_students: int
    average_class_study_duration_seconds: int
    average_class_quiz_score: Optional[float] = None
    student_overviews: List[StudentLearningOverview]


# --- Dashboard Schema ---
class DashboardSummary(BaseModel):
    my_courses: List[CourseSummaryForStudent] = []
    upcoming_quizzes: List[QuizSummaryForStudent] = []
    recent_notifications: List[NotificationInDB] = []


# --- Error Response Schema ---
class ErrorResponse(BaseModel):
    detail: str
    code: Optional[str] = None
    data: Optional[Any] = None

# Required for forward references in Pydantic models (e.g., CourseDetail referring to MaterialInDB)
# Note: For simple references with `List[X]`, Pydantic 2.x often handles this automatically.
# If you get errors related to forward references, uncomment and adjust these lines.
# CourseDetail.update_forward_refs() # If CourseDetail needs to reference MaterialInDB or QuizInDB which are defined later
# StudentAttemptDetailForStudent.update_forward_refs() # If it needs to reference QuestionForStudent which is defined later