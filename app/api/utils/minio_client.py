from minio import Minio
from minio.error import S3Error
from app.config import settings
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

# Initialize Minio client
# In production, consider using IAM roles or other secure methods for credentials
minio_client = Minio(
    settings.MINIO_ENDPOINT,
    access_key=settings.MINIO_ACCESS_KEY,
    secret_key=settings.MINIO_SECRET_KEY,
    secure=settings.MINIO_SECURE
)

def ensure_bucket_exists():
    """Ensure Minio bucket exists, create if not."""
    try:
        if not minio_client.bucket_exists(settings.MINIO_BUCKET_NAME):
            minio_client.make_bucket(settings.MINIO_BUCKET_NAME)
            logger.info(f"Minio bucket '{settings.MINIO_BUCKET_NAME}' created successfully.")
        else:
            logger.info(f"Minio bucket '{settings.MINIO_BUCKET_NAME}' already exists.")
    except S3Error as e:
        logger.critical(f"Error ensuring Minio bucket exists: {e}", exc_info=True)
        raise # Re-raise to indicate a critical setup failure

def get_presigned_upload_url(object_name: str, expiry_seconds: int = 3600) -> str:
    """
    Generate a presigned URL for clients to directly upload files to Minio.
    object_name: Full path and name of the file (e.g., "courses/1/videos/lesson1.mp4")
    expiry_seconds: URL validity period (seconds)
    """
    try:
        url = minio_client.presigned_put_object(
            bucket_name=settings.MINIO_BUCKET_NAME,
            object_name=object_name,
            expires=timedelta(seconds=expiry_seconds)
        )
        return url
    except S3Error as e:
        logger.error(f"Error generating presigned upload URL for '{object_name}': {e}")
        raise # Re-raise to be handled by API layer

def get_presigned_download_url(object_name: str, expiry_seconds: int = 3600) -> str:
    """
    Generate a presigned URL for clients to directly download files from Minio.
    object_name: Full path and name of the file
    expiry_seconds: URL validity period (seconds)
    """
    try:
        url = minio_client.presigned_get_object(
            bucket_name=settings.MINIO_BUCKET_NAME,
            object_name=object_name,
            expires=timedelta(seconds=expiry_seconds)
        )
        return url
    except S3Error as e:
        logger.error(f"Error generating presigned download URL for '{object_name}': {e}")
        raise # Re-raise to be handled by API layer

def delete_file_from_minio(object_name: str):
    """
    Delete a file from Minio.
    object_name: Full path and name of the file
    """
    try:
        minio_client.remove_object(
            bucket_name=settings.MINIO_BUCKET_NAME,
            object_name=object_name
        )
        logger.info(f"Object '{object_name}' deleted from Minio bucket '{settings.MINIO_BUCKET_NAME}'.")
    except S3Error as e:
        logger.error(f"Error deleting object '{object_name}' from Minio: {e}")
        raise # Re-raise to be handled by API layer

def file_exists_in_minio(object_name: str) -> bool:
    """Check if a file exists in Minio."""
    try:
        minio_client.stat_object(settings.MINIO_BUCKET_NAME, object_name)
        return True
    except S3Error as e:
        if e.code == 'NoSuchKey':
            return False
        logger.error(f"Error checking existence of object '{object_name}' in Minio: {e}")
        raise # Re-raise other S3 errors