from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection, utility
from app.config import settings
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional
import os
import logging

logger = logging.getLogger(__name__)

# --- Milvus Connection and Collection Management ---

COLLECTION_NAME = "learning_materials"


def init_milvus():
    """Initializes Milvus connection and ensures Collection exists on startup."""
    try:
        connections.connect(alias="default", uri=settings.MILVUS_URI)
        logger.info(f"Successfully connected to Milvu<PERSON> at {settings.MILVUS_URI}")

        if not utility.has_collection(COLLECTION_NAME):
            create_milvus_collection()
        else:
            logger.info(f"Milvus collection '{COLLECTION_NAME}' already exists.")
            collection = Collection(COLLECTION_NAME)
            if not collection.is_loaded:  # Ensure collection is loaded if it exists
                collection.load()
                logger.info(f"Milvus collection '{COLLECTION_NAME}' loaded.")

    except Exception as e:
        logger.critical(f"Error connecting to or initializing Milvus: {e}", exc_info=True)
        # In production, this might need more robust error handling or retry mechanisms
        raise


def create_milvus_collection():
    """Creates Milvus Collection and its Schema."""
    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=settings.EMBEDDING_DIM),
        FieldSchema(name="material_id", dtype=DataType.INT64),
        FieldSchema(name="course_id", dtype=DataType.INT64),
        FieldSchema(name="material_type", dtype=DataType.VARCHAR, max_length=50),
        FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name="source_text", dtype=DataType.VARCHAR, max_length=65535),  # Store original text or summary
    ]
    schema = CollectionSchema(fields, description="Learning Material Vectors")
    collection = Collection(name=COLLECTION_NAME, schema=schema)

    index_params = {
        "metric_type": "COSINE",
        "index_type": "HNSW",
        "params": {"M": 8, "efConstruction": 64}
    }
    collection.create_index("embedding", index_params)
    logger.info(f"Milvus collection '{COLLECTION_NAME}' created with HNSW index.")
    collection.load()
    logger.info(f"Milvus collection '{COLLECTION_NAME}' loaded after creation.")


def insert_vectors_into_milvus(entities: List[Dict[str, Any]]) -> List[int]:
    """
    Inserts vectors and associated metadata into Milvus.
    entities example: [{'embedding': [0.1, 0.2, ...], 'material_id': 1, 'course_id': 101, ...}]
    """
    try:
        collection = Collection(COLLECTION_NAME)
        # Prepare data for insertion based on schema fields
        insert_data = []
        for field in collection.schema.fields:
            if field.name != "id":  # 'id' is auto_id, no need to provide
                field_values = []
                for entity in entities:
                    val = entity.get(field.name)
                    # Convert enums to string for VARCHAR fields
                    if isinstance(val, enum.Enum):
                        field_values.append(val.value)
                    else:
                        field_values.append(val)
                insert_data.append(field_values)

        res = collection.insert(insert_data)
        collection.flush()
        logger.info(f"Inserted {len(res.primary_keys)} entities into Milvus.")
        return res.primary_keys
    except Exception as e:
        logger.error(f"Error inserting vectors into Milvus: {e}")
        raise


def search_milvus(query_vector: List[float], top_k: int = 10, expr: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Performs vector similarity search in Milvus.
    query_vector: The vector to search with
    top_k: Number of most similar results to return
    expr: Milvus filter expression (e.g., "course_id == 101")
    """
    try:
        collection = Collection(COLLECTION_NAME)
        # collection.load() # In most cases, it's loaded by init_milvus or in query handler, but good to ensure

        search_params = {
            "data": [query_vector],
            "anns_field": "embedding",
            "param": {"metric_type": "COSINE", "params": {"nprobe": 10}},  # Search parameters
            "limit": top_k,
            "expr": expr,
            "output_fields": ["material_id", "course_id", "title", "material_type"]
        }

        results = collection.search(**search_params)

        parsed_results = []
        for hits in results:
            for hit in hits:
                parsed_results.append({
                    "id": hit.id,
                    "distance": hit.distance,
                    "material_id": hit.entity.get('material_id'),
                    "course_id": hit.entity.get('course_id'),
                    "title": hit.entity.get('title'),
                    "material_type": hit.entity.get('material_type'),
                })
        return parsed_results
    except Exception as e:
        logger.error(f"Error searching Milvus: {e}")
        raise


# --- Text Vectorization Model ---

_model = None


def get_sentence_transformer_model():
    """Gets Sentence-Transformer model instance (singleton pattern)"""
    global _model
    if _model is None:
        model_name = "all-MiniLM-L6-v2"  # A compact and good performing model
        logger.info(f"Loading Sentence-Transformer model: {model_name}...")
        try:
            _model = SentenceTransformer(model_name)
            settings.EMBEDDING_DIM = _model.get_sentence_embedding_dimension()
            logger.info(f"Sentence-Transformer model loaded. Embedding dimension: {settings.EMBEDDING_DIM}")
        except Exception as e:
            logger.critical(f"Failed to load Sentence-Transformer model '{model_name}': {e}", exc_info=True)
            raise  # Critical failure, application cannot proceed without this.
    return _model


def vectorize_text(text: str) -> List[float]:
    """Vectorizes text using the loaded Sentence-Transformer model"""
    model = get_sentence_transformer_model()
    embeddings = model.encode(text)
    return embeddings.tolist()