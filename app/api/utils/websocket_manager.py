import asyncio
import logging
from typing import Dict, List, Optional
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class ConnectionManager:
    def __init__(self):
        # Stores all active WebSocket connections, key is user_id
        # Note: A user might be logged in on multiple devices, each with a connection.
        # In production, replace this with a distributed solution like Redis Pub/Sub
        self.active_connections: Dict[int, List[WebSocket]] = {}
        logger.info("WebSocket ConnectionManager initialized.")

    async def connect(self, websocket: WebSocket, user_id: int):
        """Establishes WebSocket connection and associates it with user_id."""
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)
        logger.info(f"User {user_id} connected. Total connections for user: {len(self.active_connections[user_id])}")

    def disconnect(self, websocket: WebSocket, user_id: int):
        """Disconnects WebSocket connection."""
        if user_id in self.active_connections and websocket in self.active_connections[user_id]:
            self.active_connections[user_id].remove(websocket)
            if not self.active_connections[user_id]: # If no more connections for this user
                del self.active_connections[user_id]
            logger.info(f"User {user_id} disconnected. Remaining connections for user: {len(self.active_connections.get(user_id, []))}")
        else:
            logger.warning(f"Attempted to disconnect a WebSocket not found for user {user_id}.")

    async def send_personal_message(self, message: str, user_id: int):
        """Sends a message to all active connections of a specific user."""
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_text(message)
                    logger.debug(f"Sent message to user {user_id}: {message[:50]}...")
                except RuntimeError as e: # Catch "WebSocket is not connected" or similar
                    logger.error(f"Failed to send message to user {user_id} via connection {connection}: {e}")
                    # In a production system, you might implement connection cleanup/re-validation here
                except Exception as e:
                    logger.exception(f"Unexpected error sending message to user {user_id}: {e}")
        else:
            logger.debug(f"User {user_id} has no active WebSocket connections to send message.")

    async def broadcast(self, message: str):
        """Broadcasts a message to all active connections."""
        # Create a list copy to safely iterate if connections are removed during iteration
        for user_id, connections in list(self.active_connections.items()):
            for connection in connections:
                try:
                    await connection.send_text(message)
                except RuntimeError as e:
                    logger.error(f"Failed to broadcast to user {user_id} via connection {connection}: {e}")
                except Exception as e:
                    logger.exception(f"Unexpected error broadcasting to user {user_id}: {e}")


manager = ConnectionManager() # Create a singleton manager instance