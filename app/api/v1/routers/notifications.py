from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List
import json
import logging

from app.database import get_db
from app.auth.dependencies import get_current_websocket_user, get_current_teacher
from app.models import User, Notification, NotificationType, Class, ClassStudent, \
    Course  # Import Class, ClassStudent, Course for target finding
from app.schemas import NotificationInDB, NotificationCreate
from app.utils.websocket_manager import manager
from app import crud
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Notifications"])


@router.websocket("/ws/notifications")
async def websocket_endpoint(websocket: WebSocket, current_user: User = Depends(get_current_websocket_user)):
    """
    WebSocket 端点，用于实时接收通知。
    客户端通过 ws://your_api_url/api/v1/ws/notifications?token=<your_jwt_token> 连接。
    """
    user_id = current_user.id
    logger.info(f"Attempting to connect WebSocket for user {user_id} ({current_user.username}).")

    try:
        await manager.connect(websocket, user_id)
        logger.info(f"WebSocket connection established for user {user_id}.")

        while True:
            # Keep the connection alive, listen for any client messages (e.g., pings)
            # In a real app, you might process specific messages from the client
            await websocket.receive_text()  # This will block until a message is received or connection closes

    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
        logger.info(f"WebSocket disconnected for user {user_id}.")
    except Exception as e:
        manager.disconnect(websocket, user_id)
        logger.error(f"WebSocket error for user {user_id}: {e}")
        # Optionally, send an error message before disconnecting if connection is still open
        # await websocket.send_text(json.dumps({"type": "error", "message": "An unexpected error occurred."}))


@router.post("/notifications", response_model=NotificationInDB, status_code=status.HTTP_201_CREATED)
async def create_and_send_notification(
        notification_data: NotificationCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端/管理员] 创建并发送一条通知。
    此API将通知保存到数据库，并尝试通过 WebSocket 实时推送。
    """
    if notification_data.target_user_id:
        target_user = crud.get_user_by_id(db, notification_data.target_user_id)
        if not target_user:
            raise NotFoundError(detail=f"Target user with ID {notification_data.target_user_id} not found.")
        # Optional: Add more granular permission, e.g., teacher can only notify their students.
        # For simplicity, admin can notify anyone, teacher can notify anyone in their classes/courses.

    if notification_data.target_class_id:
        db_class = crud.get_class_by_id(db, notification_data.target_class_id)
        if not db_class:
            raise NotFoundError(detail=f"Target class with ID {notification_data.target_class_id} not found.")
        if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
            raise ForbiddenError(detail="You do not have permission to send notifications to this class.")

    if notification_data.target_course_id:
        db_course = crud.get_course_by_id(db, notification_data.target_course_id)
        if not db_course:
            raise NotFoundError(detail=f"Target course with ID {notification_data.target_course_id} not found.")
        if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
            raise ForbiddenError(detail="You do not have permission to send notifications to this course.")

    db_notification = crud.create_notification(db, notification_data)
    logger.info(f"Notification '{db_notification.title}' created by user {current_user.id} (ID: {db_notification.id}).")

    notification_payload = json.dumps(NotificationInDB.from_orm(db_notification).dict(), ensure_ascii=False)

    if db_notification.target_user_id:
        await manager.send_personal_message(notification_payload, db_notification.target_user_id)
        logger.info(f"Notification {db_notification.id} pushed to user {db_notification.target_user_id}.")
    elif db_notification.target_class_id:
        students_in_class = crud.get_students_in_class(db, db_notification.target_class_id)
        for student in students_in_class:
            await manager.send_personal_message(notification_payload, student.id)
        logger.info(
            f"Notification {db_notification.id} pushed to {len(students_in_class)} students in class {db_notification.target_class_id}.")
    elif db_notification.target_course_id:
        students_in_course = crud.get_students_enrolled_in_course(db, db_notification.target_course_id)
        for student in students_in_course:
            await manager.send_personal_message(notification_payload, student.id)
        logger.info(
            f"Notification {db_notification.id} pushed to {len(students_in_course)} students in course {db_notification.target_course_id}.")
    elif db_notification.type == NotificationType.system:
        # Broadcasting to all active WebSocket connections (which might include teachers/admins)
        # If only students should receive system notifications, manager.broadcast needs to filter roles
        await manager.broadcast(notification_payload)
        logger.info(f"Notification {db_notification.id} broadcasted as system notification.")

    return db_notification