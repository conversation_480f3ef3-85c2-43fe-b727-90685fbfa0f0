from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.schemas import ClassCreate, ClassUpdate, ClassInDB, ClassSummaryForTeacher, AddStudentsToClassRequest, \
    StudentInClass, UserPublic
from app import crud
from app.auth.dependencies import get_current_teacher, get_current_admin
from app.models import User, UserRole, Class
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/classes", tags=["Classes"])


@router.post("/", response_model=ClassInDB, status_code=status.HTTP_201_CREATED)
async def create_new_class(
        class_data: ClassCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 创建一个新班级。
    """
    db_class = crud.create_class(db=db, class_data=class_data, teacher_id=current_user.id)
    logger.info(f"Teacher {current_user.id} created class '{db_class.name}' (ID: {db_class.id}).")
    return db_class


@router.get("/", response_model=List[ClassSummaryForTeacher])
async def read_classes(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        search: Optional[str] = Query(None, min_length=1, max_length=100),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取班级列表。教师只能看到自己创建的班级，管理员可以查看所有班级。
    支持分页和按名称搜索。
    """
    teacher_id_filter: Optional[int] = None
    if current_user.role == UserRole.teacher:
        teacher_id_filter = current_user.id

    classes_with_count = crud.get_classes(db=db, skip=skip, limit=limit, teacher_id=teacher_id_filter,
                                          search_query=search)

    result_classes = []
    for db_class, student_count in classes_with_count:
        teacher_name = db_class.teacher.full_name if db_class.teacher else "Unknown Teacher"
        result_classes.append(ClassSummaryForTeacher(
            id=db_class.id,
            name=db_class.name,
            teacher_id=db_class.teacher_id,
            created_at=db_class.created_at,
            updated_at=db_class.updated_at,
            teacher_name=teacher_name,
            student_count=student_count
        ))
    logger.info(f"User {current_user.id} fetched {len(result_classes)} classes.")
    return result_classes


@router.get("/{class_id}", response_model=ClassSummaryForTeacher)
async def read_class(
        class_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取班级详情。
    """
    db_class_with_count = crud.get_class_with_student_count(db, class_id=class_id)
    if not db_class_with_count:
        raise NotFoundError(detail="Class not found.")

    db_class, student_count = db_class_with_count

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this class.")

    teacher_name = db_class.teacher.full_name if db_class.teacher else "Unknown Teacher"
    logger.info(f"User {current_user.id} fetched class {class_id} details.")
    return ClassSummaryForTeacher(
        id=db_class.id,
        name=db_class.name,
        teacher_id=db_class.teacher_id,
        created_at=db_class.created_at,
        updated_at=db_class.updated_at,
        teacher_name=teacher_name,
        student_count=student_count
    )


@router.put("/{class_id}", response_model=ClassInDB)
async def update_class_info(
        class_id: int = Query(..., gt=0),
        class_update: ClassUpdate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新班级信息（如名称）。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this class.")

    updated_class = crud.update_class(db, db_class, class_update)
    logger.info(f"User {current_user.id} updated class {class_id}.")
    return updated_class


@router.delete("/{class_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_class_by_id(
        class_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除班级。此操作会同时删除班级与学生的关联。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this class.")

    deleted_class = crud.delete_class(db, class_id)
    if not deleted_class:
        raise InternalServerError(detail="Failed to delete class record from database.")
    logger.info(f"User {current_user.id} deleted class {class_id}.")
    return


@router.post("/{class_id}/students", response_model=List[StudentInClass], status_code=status.HTTP_200_OK)
async def add_students_to_specific_class(
        class_id: int = Query(..., gt=0),
        request: AddStudentsToClassRequest,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 将学生添加到指定班级。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to manage students in this class.")

    valid_student_ids = []
    invalid_student_ids = []
    for s_id in request.student_ids:
        user = crud.get_user_by_id(db, s_id)
        if user and user.role == UserRole.student:
            valid_student_ids.append(s_id)
        else:
            invalid_student_ids.append(s_id)

    if invalid_student_ids:
        raise BadRequestError(
            detail=f"Some provided IDs are not valid student IDs or do not exist.",
            code="INVALID_STUDENT_IDS",
            data={"invalid_ids": invalid_student_ids}
        )

    added_class_students = crud.add_students_to_class(db, class_id, valid_student_ids)

    response_students = []
    for cs in added_class_students:
        student_user = crud.get_user_by_id(db, cs.student_id)
        if student_user:  # Should always be true due to prior validation
            response_students.append(StudentInClass(
                id=student_user.id,
                username=student_user.username,
                full_name=student_user.full_name,
                email=student_user.email,
                joined_at=cs.joined_at
            ))
    logger.info(f"User {current_user.id} added {len(valid_student_ids)} students to class {class_id}.")
    return response_students


@router.delete("/{class_id}/students/{student_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_student_from_specific_class(
        class_id: int = Query(..., gt=0),
        student_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 从指定班级中移除学生。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to manage students in this class.")

    success = crud.remove_student_from_class(db, class_id, student_id)
    if not success:
        raise NotFoundError(detail="Student not found in this class.", code="STUDENT_NOT_IN_CLASS")
    logger.info(f"User {current_user.id} removed student {student_id} from class {class_id}.")
    return


@router.get("/{class_id}/students", response_model=List[UserPublic])
async def get_students_in_specific_class(
        class_id: int = Query(..., gt=0),
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取指定班级内的所有学生列表。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view students in this class.")

    students = crud.get_students_in_class(db, class_id, skip=skip, limit=limit)
    return students