from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import json
import logging

from app.database import get_db
from app.schemas import (
    DashboardSummary, CourseSummaryForStudent, QuizSummaryForStudent, NotificationInDB,
    CourseDetailForStudent, MaterialInCourseForStudent, StudentMaterialProgressUpdate,
    StudentAttemptCreate, StudentAttemptInDB, StudentAttemptDetailForStudent,
    QuestionForStudentWithAttemptInfo  # Import the extended schema
)
from app import crud
from app.auth.dependencies import get_current_active_user
from app.models import User, UserRole, Class, ClassStudent, QuizQuestion, QuestionType, StudentQuizAttempt
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/students", tags=["Students"])


@router.get("/me/dashboard_summary", response_model=DashboardSummary)
async def get_my_dashboard_summary(
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取个人学习仪表盘总览数据。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access this dashboard.")

    student_id = current_user.id

    my_courses_data = crud.get_student_enrolled_courses(db, student_id)
    my_courses_summary: List[CourseSummaryForStudent] = []
    for course in my_courses_data:
        progress = crud.get_student_course_progress(db, student_id, course.id)
        my_courses_summary.append(CourseSummaryForStudent(
            id=course.id,
            title=course.title,
            description=course.description,
            cover_image_url=course.cover_image_url,
            teacher_id=course.teacher_id,
            status=course.status,
            my_progress_percentage=progress
        ))

    upcoming_quizzes_data = crud.get_upcoming_quizzes_for_student(db, student_id)
    upcoming_quizzes_summary: List[QuizSummaryForStudent] = []
    for quiz in upcoming_quizzes_data:
        attempt = db.query(StudentQuizAttempt).filter(
            StudentQuizAttempt.student_id == student_id,
            StudentQuizAttempt.quiz_id == quiz.id
        ).first()

        upcoming_quizzes_summary.append(QuizSummaryForStudent(
            id=quiz.id,
            title=quiz.title,
            description=quiz.description,
            quiz_type=quiz.quiz_type,
            due_date=quiz.due_date,
            is_published=quiz.is_published,
            is_completed_by_me=bool(attempt),
            my_score=attempt.score if attempt else None
        ))

    recent_notifications_data = crud.get_notifications_for_user(db, student_id)
    recent_notifications_summary: List[NotificationInDB] = []
    for notif in recent_notifications_data:
        recent_notifications_summary.append(NotificationInDB.from_orm(notif))

    logger.info(f"Student {student_id} fetched dashboard summary.")
    return DashboardSummary(
        my_courses=my_courses_summary,
        upcoming_quizzes=upcoming_quizzes_summary,
        recent_notifications=recent_notifications_summary
    )


@router.get("/me/courses", response_model=List[CourseSummaryForStudent])
async def get_my_courses(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取我所有已加入的课程列表及其进度。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access their courses.")

    student_id = current_user.id
    my_courses_data = crud.get_student_enrolled_courses(db, student_id, skip=skip, limit=limit)
    my_courses_summary: List[CourseSummaryForStudent] = []
    for course in my_courses_data:
        progress = crud.get_student_course_progress(db, student_id, course.id)
        my_courses_summary.append(CourseSummaryForStudent(
            id=course.id,
            title=course.title,
            description=course.description,
            cover_image_url=course.cover_image_url,
            teacher_id=course.teacher_id,
            status=course.status,
            my_progress_percentage=progress
        ))
    return my_courses_summary


@router.get("/me/quizzes", response_model=List[QuizSummaryForStudent])
async def get_my_quizzes(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取与我相关的测验/作业列表（包括已完成和未完成）。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access their quizzes.")

    student_id = current_user.id

    student_courses = crud.get_student_enrolled_courses(db, student_id)
    course_ids = [c.id for c in student_courses]

    if not course_ids:
        return []

    all_related_quizzes = db.query(Quiz).filter(Quiz.course_id.in_(course_ids), Quiz.is_published == True).all()

    quizzes_summary: List[QuizSummaryForStudent] = []
    for quiz in all_related_quizzes:
        attempt = db.query(StudentQuizAttempt).filter(
            StudentQuizAttempt.student_id == student_id,
            StudentQuizAttempt.quiz_id == quiz.id
        ).first()

        quizzes_summary.append(QuizSummaryForStudent(
            id=quiz.id,
            title=quiz.title,
            description=quiz.description,
            quiz_type=quiz.quiz_type,
            due_date=quiz.due_date,
            is_published=quiz.is_published,
            is_completed_by_me=bool(attempt),
            my_score=attempt.score if attempt else None
        ))

    return sorted(quizzes_summary, key=lambda q: q.due_date if q.due_date else datetime.max)


@router.get("/me/notifications", response_model=List[NotificationInDB])
async def get_my_notifications(
        skip: int = Query(0, ge=0),
        limit: int = Query(10, gt=0, le=100),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取我的所有通知列表。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access their notifications.")

    notifications_data = crud.get_notifications_for_user(db, current_user.id, limit=limit)
    return [NotificationInDB.from_orm(notif) for notif in notifications_data]


@router.put("/me/notifications/{notification_id}/mark_read", response_model=NotificationInDB)
async def mark_my_notification_read(
        notification_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 标记我的某个通知为已读。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can mark notifications as read.")

    updated_notification = crud.mark_notification_as_read(db, notification_id, current_user.id)
    if not updated_notification:
        raise NotFoundError(detail="Notification not found or you don't have permission to mark it.")

    return NotificationInDB.from_orm(updated_notification)


@router.get("/courses/{course_id}", response_model=CourseDetailForStudent)
async def get_course_detail_for_student(
        course_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取课程详情，包括可访问的资料列表及其学习进度，以及相关的测验列表。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access course details.")

    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
        ClassStudent.student_id == current_user.id).distinct().all()
    student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}

    if db_course.teacher_id not in student_class_teacher_ids:
        raise ForbiddenError(detail="You are not enrolled in this course or do not have access.")

    materials_with_progress = crud.get_materials_for_course_by_student(db, course_id, current_user.id)
    materials_summary: List[MaterialInCourseForStudent] = []
    for material in materials_with_progress:
        progress_obj = crud.get_student_material_progress(db, current_user.id, material.id)

        materials_summary.append(MaterialInCourseForStudent(
            id=material.id,
            title=material.title,
            description=material.description,
            file_type=material.file_type,
            file_path_minio=material.file_path_minio,  # Not exposed to frontend
            preview_url=material.preview_url,
            duration_seconds=material.duration_seconds,
            my_progress_percentage=progress_obj.progress_percentage if progress_obj else 0.0,
            my_last_viewed_timestamp=progress_obj.last_viewed_timestamp if progress_obj else None,
            my_total_view_duration_seconds=progress_obj.total_view_duration_seconds if progress_obj else 0,
            my_is_completed=progress_obj.is_completed if progress_obj else False
        ))

    quizzes_data = crud.get_quizzes_by_course_id(db, course_id=course_id)
    quizzes_summary: List[QuizSummaryForStudent] = []
    for quiz in quizzes_data:
        attempt = db.query(StudentQuizAttempt).filter(
            StudentQuizAttempt.student_id == current_user.id,
            StudentQuizAttempt.quiz_id == quiz.id
        ).first()

        quizzes_summary.append(QuizSummaryForStudent(
            id=quiz.id,
            title=quiz.title,
            description=quiz.description,
            quiz_type=quiz.quiz_type,
            due_date=quiz.due_date,
            is_published=quiz.is_published,
            is_completed_by_me=bool(attempt),
            my_score=attempt.score if attempt else None
        ))

    teacher_name = db_course.teacher.full_name if db_course.teacher else "Unknown Teacher"

    return CourseDetailForStudent(
        id=db_course.id,
        title=db_course.title,
        description=db_course.description,
        cover_image_url=db_course.cover_image_url,
        teacher_id=db_course.teacher_id,
        status=db_course.status,
        created_at=db_course.created_at,
        updated_at=db_course.updated_at,
        teacher_name=teacher_name,
        materials=materials_summary,
        quizzes=quizzes_summary
    )


@router.get("/materials/{material_id}", response_model=MaterialInCourseForStudent)
async def get_material_detail_for_student(
        material_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取单个教学资料的详细信息，包括下载URL和我的学习进度。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access material details.")

    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material:
        raise NotFoundError(detail="Material not found.")

    db_course = db_material.course
    if not db_course:
        logger.error(f"Material {material_id} has no associated course. Data integrity issue.")
        raise InternalServerError(detail="Material's course not found. Data integrity issue.")

    student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
        ClassStudent.student_id == current_user.id).distinct().all()
    student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}

    if db_course.teacher_id not in student_class_teacher_ids:
        raise ForbiddenError(detail="You do not have access to this material.")

    progress_obj = crud.get_student_material_progress(db, current_user.id, material_id)

    material_data = MaterialInCourseForStudent(
        id=db_material.id,
        title=db_material.title,
        description=db_material.description,
        file_type=db_material.file_type,
        file_path_minio=db_material.file_path_minio,
        preview_url=db_material.preview_url,
        duration_seconds=db_material.duration_seconds,
        my_progress_percentage=progress_obj.progress_percentage if progress_obj else 0.0,
        my_last_viewed_timestamp=progress_obj.last_viewed_timestamp if progress_obj else None,
        my_total_view_duration_seconds=progress_obj.total_view_duration_seconds if progress_obj else 0,
        my_is_completed=progress_obj.is_completed if progress_obj else False
    )
    return material_data


@router.post("/materials/{material_id}/progress", response_model=StudentMaterialProgressUpdate)
async def update_material_progress(
        material_id: int = Query(..., gt=0),
        progress_data: StudentMaterialProgressUpdate,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 更新学生在某个教学资料的学习进度。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can update progress.")

    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material:
        raise NotFoundError(detail="Material not found.")

    db_course = db_material.course
    if not db_course:
        logger.error(f"Material {material_id} has no associated course. Data integrity issue.")
        raise InternalServerError(detail="Material's course not found. Data integrity issue.")

    student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
        ClassStudent.student_id == current_user.id).distinct().all()
    student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}

    if db_course.teacher_id not in student_class_teacher_ids:
        raise ForbiddenError(detail="You do not have access to this material.")

    updated_progress = crud.update_or_create_student_material_progress(
        db=db,
        student_id=current_user.id,
        material_id=material_id,
        progress_data=progress_data
    )
    # Return a subset of the actual ORM object to match StudentMaterialProgressUpdate schema
    return StudentMaterialProgressUpdate(
        progress_percentage=updated_progress.progress_percentage,
        last_viewed_timestamp=updated_progress.last_viewed_timestamp,
        view_duration_increment=updated_progress.total_view_duration_seconds,
        # Note: this is total duration, not increment
        is_completed=updated_progress.is_completed
    )


@router.get("/quizzes/{quiz_id}", response_model=QuizInDBForStudent)
async def get_quiz_for_student(
        quiz_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取一个测验/作业的详细信息和所有问题（不包含正确答案）。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access quizzes.")

    db_quiz = crud.get_quiz_for_student_with_questions(db, quiz_id=quiz_id)
    if not db_quiz or not db_quiz.is_published:
        raise NotFoundError(detail="Quiz not found or not published.")

    db_course = db_quiz.course
    if not db_course:
        logger.error(f"Quiz {quiz_id} has no associated course. Data integrity issue.")
        raise InternalServerError(detail="Quiz's course not found. Data integrity issue.")

    student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
        ClassStudent.student_id == current_user.id).distinct().all()
    student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}

    if db_course.teacher_id not in student_class_teacher_ids:
        raise ForbiddenError(detail="You do not have access to this quiz.")

    questions_for_student = []
    for q in db_quiz.questions:
        q_dict = q.__dict__.copy()
        if 'correct_answer' in q_dict:
            del q_dict['correct_answer']
        if q.options:
            q_dict['options'] = json.loads(q.options)
        questions_for_student.append(QuestionForStudent(
            id=q.id,
            question_text=q.question_text,
            question_type=q.question_type,
            options=q_dict.get('options'),
            question_order=q.question_order,
            score=q.score
        ))

    response_quiz = QuizInDBForStudent(
        id=db_quiz.id,
        title=db_quiz.title,
        description=db_quiz.description,
        quiz_type=db_quiz.quiz_type,
        due_date=db_quiz.due_date,
        is_published=db_quiz.is_published,
        course_id=db_quiz.course_id,
        created_by_user_id=db_quiz.created_by_user_id,
        created_at=db_quiz.created_at,
        updated_at=db_quiz.updated_at,
        questions=questions_for_student
    )
    return response_quiz


@router.post("/quizzes/{quiz_id}/submit", response_model=StudentAttemptInDB, status_code=status.HTTP_201_CREATED)
async def submit_quiz_attempt(
        quiz_id: int = Query(..., gt=0),
        attempt_data: StudentAttemptCreate,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 学生提交测验/作业。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only students can submit attempts.")

    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz or not db_quiz.is_published:
        raise BadRequestError(detail="Quiz not found or not published.")

    if db_quiz.due_date and datetime.now(db_quiz.due_date.tzinfo) > db_quiz.due_date:
        raise BadRequestError(detail="This quiz has passed its due date.", code="QUIZ_DUE_DATE_PASSED")

    existing_attempt = db.query(StudentQuizAttempt).filter(
        StudentQuizAttempt.student_id == current_user.id,
        StudentQuizAttempt.quiz_id == quiz_id
    ).first()
    if existing_attempt:
        raise BadRequestError(detail="You have already submitted this quiz.", code="QUIZ_ALREADY_SUBMITTED")

    quiz_questions = crud.get_quiz_with_questions(db, quiz_id)
    if not quiz_questions or not quiz_questions.questions:
        raise NotFoundError(detail="Quiz has no questions.")

    valid_question_ids = {q.id for q in quiz_questions.questions}
    for submitted_ans in attempt_data.submitted_answers:
        if submitted_ans.question_id not in valid_question_ids:
            raise BadRequestError(detail=f"Question ID {submitted_ans.question_id} does not belong to this quiz.",
                                  code="INVALID_QUESTION_ID_IN_SUBMISSION")

    db_attempt = crud.create_student_quiz_attempt(
        db=db,
        student_id=current_user.id,
        quiz_id=quiz_id,
        submitted_answers_data=[ans.dict() for ans in attempt_data.submitted_answers]
    )
    if db_attempt.submitted_answers:
        db_attempt.submitted_answers = json.loads(db_attempt.submitted_answers)
    logger.info(f"Student {current_user.id} submitted attempt {db_attempt.id} for quiz {quiz_id}.")
    return db_attempt


@router.get("/quizzes/attempts/{attempt_id}", response_model=StudentAttemptDetailForStudent)
async def get_my_quiz_attempt_detail(
        attempt_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生端] 获取我的某个测验尝试的详情，包括我的答案和客观题的对错。
    """
    if current_user.role != UserRole.student:
        raise ForbiddenError(detail="Only student users can access their attempts.")

    db_attempt = crud.get_student_quiz_attempt_with_questions(db, attempt_id=attempt_id)
    if not db_attempt:
        raise NotFoundError(detail="Attempt not found.")

    if db_attempt.student_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this attempt.")

    submitted_answers_dict = {}
    if db_attempt.submitted_answers:
        submitted_answers_dict = json.loads(db_attempt.submitted_answers)

    questions_answered_list: List[QuestionForStudentWithAttemptInfo] = []
    for question in db_attempt.quiz.questions:
        submitted_answer = submitted_answers_dict.get(str(question.id))

        q_dict_raw = question.__dict__.copy()
        if 'correct_answer' in q_dict_raw:
            del q_dict_raw['correct_answer']
        if q_dict_raw['options']:
            q_dict_raw['options'] = json.loads(q_dict_raw['options'])

        is_correct = None
        correct_ans_actual = None  # For internal comparison

        if question.correct_answer:
            try:
                correct_ans_actual = json.loads(question.correct_answer)
            except json.JSONDecodeError:
                logger.error(
                    f"Error parsing correct_answer for question {question.id} in attempt detail: {question.correct_answer}")

        if question.question_type == QuestionType.single_choice:
            is_correct = (submitted_answer == correct_ans_actual)
        elif question.question_type == QuestionType.multiple_choice:
            submitted_list = sorted(submitted_answer) if isinstance(submitted_answer, list) else []
            correct_list = sorted(correct_ans_actual) if correct_ans_actual and isinstance(correct_ans_actual,
                                                                                           list) else []
            is_correct = (submitted_list == correct_list)
        elif question.question_type == QuestionType.fill_in_blank:
            is_correct = (str(submitted_answer).strip().lower() == str(correct_ans_actual).strip().lower())

        # Build QuestionForStudentWithAttemptInfo instance
        questions_answered_list.append(QuestionForStudentWithAttemptInfo(
            id=question.id,
            question_text=question.question_text,
            question_type=question.question_type,
            options=q_dict_raw.get('options'),
            question_order=question.question_order,
            score=question.score,
            submitted_answer=submitted_answer,
            is_correct=is_correct
        ))

    return StudentAttemptDetailForStudent(
        id=db_attempt.id,
        student_id=db_attempt.student_id,
        quiz_id=db_attempt.quiz_id,
        score=db_attempt.score,
        submitted_answers=submitted_answers_dict,
        start_time=db_attempt.start_time,
        submit_time=db_attempt.submit_time,
        is_completed=db_attempt.is_completed,
        created_at=db_attempt.created_at,
        updated_at=db_attempt.updated_at,
        quiz_title=db_attempt.quiz.title,
        questions_answered=questions_answered_list
    )