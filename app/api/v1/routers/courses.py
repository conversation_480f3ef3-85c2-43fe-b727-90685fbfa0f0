from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.schemas import CourseCreate, CourseUpdate, CourseInDB, CourseDetail, CourseSummaryForTeacher
from app import crud
from app.auth.dependencies import get_current_teacher
from app.models import User, UserRole
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/courses", tags=["Courses"])


@router.post("/", response_model=CourseInDB, status_code=status.HTTP_201_CREATED)
async def create_new_course(
        course: CourseCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 创建一门新课程。
    """
    db_course = crud.create_course(db=db, course=course, teacher_id=current_user.id)
    logger.info(f"Teacher {current_user.id} created course '{db_course.title}' (ID: {db_course.id}).")
    return db_course


@router.get("/", response_model=List[CourseSummaryForTeacher])
async def read_courses(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        search: Optional[str] = Query(None, min_length=1, max_length=100),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取课程列表。教师只能看到自己创建的课程，管理员可以查看所有课程。
    支持分页和按标题/描述搜索。
    """
    teacher_id_filter: Optional[int] = None
    if current_user.role == UserRole.teacher:
        teacher_id_filter = current_user.id

    courses = crud.get_courses(db=db, skip=skip, limit=limit, teacher_id=teacher_id_filter, search_query=search)

    result_courses = []
    for course in courses:
        teacher_name = course.teacher.full_name if course.teacher else "Unknown Teacher"
        result_courses.append(CourseSummaryForTeacher(
            id=course.id,
            title=course.title,
            description=course.description,
            cover_image_url=course.cover_image_url,
            teacher_id=course.teacher_id,
            status=course.status,
            created_at=course.created_at,
            updated_at=course.updated_at,
            teacher_name=teacher_name
        ))
    logger.info(f"User {current_user.id} fetched {len(result_courses)} courses.")
    return result_courses


@router.get("/{course_id}", response_model=CourseDetail)
async def read_course(
        course_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取课程详情，包括其关联的资料和测验。
    """
    db_course = crud.get_course_with_relations_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this course.")

    logger.info(f"User {current_user.id} fetched course {course_id} details.")
    return db_course


@router.put("/{course_id}", response_model=CourseInDB)
async def update_course_info(
        course_id: int = Query(..., gt=0),
        course_update: CourseUpdate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新课程信息。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this course.")

    updated_course = crud.update_course(db, db_course, course_update)
    logger.info(f"User {current_user.id} updated course {course_id}.")
    return updated_course


@router.delete("/{course_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_course_by_id(
        course_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除课程。
    注意：此操作目前不级联删除关联的资料和测验。生产环境中需要更复杂的逻辑。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this course.")

    deleted_course = crud.delete_course(db, course_id)
    if not deleted_course:
        raise InternalServerError(detail="Failed to delete course record from database.")
    logger.info(f"User {current_user.id} deleted course {course_id}.")
    return