from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.schemas import UserPublic, UserUpdate
from app import crud
from app.auth.dependencies import get_current_active_user, get_current_admin
from app.models import User, UserRole
from app.exceptions import BadRequestError, ForbiddenError, NotFoundError, InternalServerError
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/users", tags=["Users"])

@router.get("/me", response_model=UserPublic)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """
    获取当前登录用户的信息。
    """
    logger.info(f"User {current_user.id} requested own profile.")
    return current_user

@router.put("/me", response_model=UserPublic)
async def update_users_me(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新当前登录用户的信息。
    """
    updated_user = crud.update_user(db, current_user, user_update)
    logger.info(f"User {current_user.id} updated own profile.")
    return updated_user

@router.get("/", response_model=List[UserPublic])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    role: Optional[UserRole] = None,
    current_user: User = Depends(get_current_admin), # 仅管理员可访问
    db: Session = Depends(get_db)
):
    """
    [管理员] 获取所有用户列表，支持搜索和按角色过滤。
    """
    # get_current_admin 依赖已经强制了角色为 Admin
    users = crud.get_users(db=db, skip=skip, limit=limit, search_query=search, role=role)
    logger.info(f"Admin {current_user.id} fetched {len(users)} users.")
    return users