from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.auth.dependencies import get_current_admin
from app.models import User, UserRole, Course, Class, Material, Quiz, Notification
from app.schemas import (
    UserPublic, UserCreate, UserAdminUpdate, CourseInDB, ClassInDB, MaterialInDB, QuizInDB, NotificationInDB
)
from app import crud
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
from app.utils.minio_client import delete_file_from_minio  # For admin material delete
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/admin", tags=["Admin"])


# --- User Management (Admin Only) ---
@router.post("/users", response_model=UserPublic, status_code=status.HTTP_201_CREATED)
async def admin_create_user(
        user_data: User<PERSON>reate,
        current_user: User = Depends(get_current_admin),
        db: Session = Depends(get_db)
):
    """
    [管理员] 创建一个新用户，可以指定任意角色。
    """
    db_user_by_username = crud.get_user_by_username(db, username=user_data.username)
    if db_user_by_username:
        raise BadRequestError(detail="Username already registered.", code="USERNAME_EXISTS")

    if user_data.email:
        db_user_by_email = crud.get_user_by_email(db, email=user_data.email)
        if db_user_by_email:
            raise BadRequestError(detail="Email already registered.", code="EMAIL_EXISTS")

    new_user = crud.create_user(db=db, user=user_data)
    logger.info(f"Admin {current_user.id} created user {new_user.id} ({new_user.role.value}).")
    return new_user


@router.get("/users", response_model=List[UserPublic])
async def admin_get_all_users(
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        search: Optional[str] = Query(None, min_length=1, max_length=100),
        role: Optional[UserRole] = None,
        current_user: User = Depends(get_current_admin),
        db: Session = Depends(get_db)
):
    """
    [管理员] 获取所有用户列表，支持搜索和按角色过滤。
    """
    users = crud.get_users(db=db, skip=skip, limit=limit, search_query=search, role=role)
    logger.info(f"Admin {current_user.id} fetched {len(users)} users.")
    return users


@router.get("/users/{user_id}", response_model=UserPublic)
async def admin_get_user_by_id(
        user_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin),
        db: Session = Depends(get_db)
):
    """
    [管理员] 根据ID获取用户详情。
    """
    db_user = crud.get_user_by_id(db, user_id=user_id)
    if not db_user:
        raise NotFoundError(detail="User not found.")
    return db_user


@router.put("/users/{user_id}", response_model=UserPublic)
async def admin_update_user_info(
        user_id: int = Query(..., gt=0),
        user_update: UserAdminUpdate,
        current_user: User = Depends(get_current_admin),
        db: Session = Depends(get_db)
):
    """
    [管理员] 更新用户信息，包括角色和激活状态。
    """
    db_user = crud.get_user_by_id(db, user_id=user_id)
    if not db_user:
        raise NotFoundError(detail="User not found.")

    if user_id == current_user.id and user_update.role is not None and user_update.role != current_user.role:
        raise BadRequestError(detail="You cannot change your own role.", code="SELF_ROLE_CHANGE_FORBIDDEN")

    if user_id == current_user.id and user_update.is_active is False:
        raise BadRequestError(detail="You cannot deactivate your own account.", code="SELF_DEACTIVATE_FORBIDDEN")

    updated_user = crud.admin_update_user(db, db_user, user_update)
    logger.info(f"Admin {current_user.id} updated user {user_id}.")
    return updated_user


@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_user_by_id(
        user_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin),
        db: Session = Depends(get_db)
):
    """
    [管理员] 删除用户。
    注意：此操作应谨慎，需要处理所有关联数据的级联删除或置空。
    """
    if user_id == current_user.id:
        raise BadRequestError(detail="You cannot delete yourself.", code="SELF_DELETE_FORBIDDEN")

    db_user = crud.get_user_by_id(db, user_id=user_id)
    if not db_user:
        raise NotFoundError(detail="User not found.")

    deleted_user = crud.admin_delete_user(db, user_id)
    if not deleted_user:
        raise InternalServerError(detail="Failed to delete user.")
    logger.info(f"Admin {current_user.id} deleted user {user_id}.")
    return


# --- Course Management (Admin Override) ---
@router.get("/courses", response_model=List[CourseInDB])
async def admin_get_all_courses(
        skip: int = Query(0, ge=0), limit: int = Query(100, gt=0, le=200),
        search: Optional[str] = Query(None, min_length=1, max_length=100),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 获取所有课程列表。"""
    courses = crud.get_courses(db=db, skip=skip, limit=limit, search_query=search)
    return courses


@router.put("/courses/{course_id}", response_model=CourseInDB)
async def admin_update_course_info(
        course_id: int = Query(..., gt=0), course_update: CourseUpdate,
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 更新任意课程信息。"""
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course: raise NotFoundError(detail="Course not found.")
    updated_course = crud.update_course(db, db_course, course_update)
    logger.info(f"Admin {current_user.id} updated course {course_id}.")
    return updated_course


@router.delete("/courses/{course_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_course_by_id(
        course_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 删除任意课程。注意：不处理级联删除。"""
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course: raise NotFoundError(detail="Course not found.")
    deleted_course = crud.delete_course(db, course_id)
    if not deleted_course: raise InternalServerError(detail="Failed to delete course.")
    logger.info(f"Admin {current_user.id} deleted course {course_id}.")
    return


# --- Class Management (Admin Override) ---
@router.get("/classes", response_model=List[ClassInDB])
async def admin_get_all_classes(
        skip: int = Query(0, ge=0), limit: int = Query(100, gt=0, le=200),
        search: Optional[str] = Query(None, min_length=1, max_length=100),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 获取所有班级列表。"""
    classes_with_count = crud.get_classes(db=db, skip=skip, limit=limit, search_query=search)
    return [ClassInDB.from_orm(c) for c, _ in classes_with_count]


@router.put("/classes/{class_id}", response_model=ClassInDB)
async def admin_update_class_info(
        class_id: int = Query(..., gt=0), class_update: ClassUpdate,
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 更新任意班级信息。"""
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class: raise NotFoundError(detail="Class not found.")
    updated_class = crud.update_class(db, db_class, class_update)
    logger.info(f"Admin {current_user.id} updated class {class_id}.")
    return updated_class


@router.delete("/classes/{class_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_class_by_id(
        class_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 删除任意班级。"""
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class: raise NotFoundError(detail="Class not found.")
    deleted_class = crud.delete_class(db, class_id)
    if not deleted_class: raise InternalServerError(detail="Failed to delete class.")
    logger.info(f"Admin {current_user.id} deleted class {class_id}.")
    return


# --- Material Management (Admin Override) ---
@router.delete("/materials/{material_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_material_by_id(
        material_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 删除任意资料。"""
    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material: raise NotFoundError(detail="Material not found.")

    try:
        delete_file_from_minio(db_material.file_path_minio)
        logger.info(f"Admin {current_user.id} successfully deleted Minio file for material {material_id}.")
    except Exception as e:
        logger.error(f"Admin {current_user.id} failed to delete Minio file for material {material_id}: {e}")
        # Decide if this should block the DB deletion or just warn
        raise InternalServerError(detail=f"Failed to delete file from storage: {e}")

    deleted_material = crud.delete_material(db, material_id)
    if not deleted_material: raise InternalServerError(detail="Failed to delete material record.")
    logger.info(f"Admin {current_user.id} deleted material {material_id}.")
    return


# --- Quiz Management (Admin Override) ---
@router.delete("/quizzes/{quiz_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_quiz_by_id(
        quiz_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_admin), db: Session = Depends(get_db)
):
    """[管理员] 删除任意测验。"""
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz: raise NotFoundError(detail="Quiz not found.")
    deleted_quiz = crud.delete_quiz(db, quiz_id)
    if not deleted_quiz: raise InternalServerError(detail="Failed to delete quiz.")
    logger.info(f"Admin {current_user.id} deleted quiz {quiz_id}.")
    return