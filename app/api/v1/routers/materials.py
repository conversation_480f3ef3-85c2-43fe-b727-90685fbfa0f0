from fastapi import APIRouter, Depends, BackgroundTasks, status, Query, Path
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.auth.dependencies import get_current_teacher, get_current_active_user
from app.models import User, Course, Material, MaterialType, UserRole, Class, \
    ClassStudent  # 导入 Class, ClassStudent for permission checks
from app.schemas import PresignedUploadUrlResponse, PresignedDownloadUrlResponse, MaterialCreate, MaterialInDB, \
    MaterialUploadedConfirmation, MaterialUpdate, MaterialSearchQuery, MaterialSearchResult, MaterialInCourseForStudent
from app import crud
from app.utils.minio_client import get_presigned_upload_url, get_presigned_download_url, delete_file_from_minio, \
    file_exists_in_minio
from app.utils.milvus_client_utils import vectorize_text, insert_vectors_into_milvus, search_milvus
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError
import os
import uuid
import requests  # Used in background task for text extraction
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/materials", tags=["Materials"])


# Helper function: Generate a unique filename and Minio object path
def generate_minio_object_name(course_id: int, original_filename: str, file_type: MaterialType) -> str:
    file_extension = os.path.splitext(original_filename)[1]
    unique_id = str(uuid.uuid4())
    # Construct Minio object path: e.g., courses/{course_id}/{file_type.value}/{unique_id}{.ext}
    return f"courses/{course_id}/{file_type.value}/{unique_id}{file_extension}"


# --- Async Task: Process uploaded material for text extraction and vectorization ---
def process_material_for_vectorization_async(material_id: int, file_path_minio: str, file_type: MaterialType):
    """
    Asynchronous task: Processes uploaded material, extracts text/features, vectorizes it, and inserts into Milvus.
    Note: In production, this task should ideally be handled by a separate Worker service
          (e.g., using Celery with a message queue) to avoid blocking the web server.
    """
    logger.info(
        f"[{datetime.now()}] Starting async vectorization for material {material_id} ({file_path_minio}) of type {file_type.value}")

    from app.database import SessionLocal  # Import SessionLocal here to get a new DB session
    db = SessionLocal()
    try:
        db_material = crud.get_material_by_id(db, material_id)
        if not db_material:
            logger.warning(
                f"[{datetime.now()}] Material {material_id} not found for vectorization (might have been deleted).")
            return

        extracted_text = ""
        # Base text: title and description
        extracted_text = db_material.title + " " + (db_material.description or "")

        # Attempt to download and extract more content for text-based files
        if file_type in [MaterialType.text, MaterialType.pdf, MaterialType.word, MaterialType.ppt]:
            try:
                # Get a temporary download URL (short expiry)
                download_url = get_presigned_download_url(file_path_minio, expiry_seconds=330)
                response = requests.get(download_url, timeout=60)  # Set a reasonable timeout
                response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)

                if file_type == MaterialType.text:
                    extracted_text += "\n" + response.text[:64000]  # Truncate to avoid exceeding Milvus limit
                elif file_type == MaterialType.pdf:
                    # Placeholder for actual PDF text extraction (requires 'pdfminer.six' or similar)
                    extracted_text += f"\n(Placeholder for PDF text content from {file_path_minio})"
                elif file_type == MaterialType.word:
                    # Placeholder for actual Word text extraction (requires 'python-docx' or similar)
                    extracted_text += f"\n(Placeholder for Word text content from {file_path_minio})"
                elif file_type == MaterialType.ppt:
                    # Placeholder for actual PPT text extraction (requires 'python-pptx' or similar)
                    extracted_text += f"\n(Placeholder for PPT text content from {file_path_minio})"
                # For video/audio, you would typically integrate with an ASR service here
                elif file_type == MaterialType.video:
                    extracted_text += f"\n(Placeholder for video transcript, requires ASR service)"
                elif file_type == MaterialType.audio:
                    extracted_text += f"\n(Placeholder for audio transcript, requires ASR service)"


            except requests.exceptions.RequestException as e:
                logger.error(
                    f"[{datetime.now()}] Failed to download or read content for material {material_id} from Minio: {e}")
                # Fallback to just title/description if content extraction fails
            except Exception as e:
                logger.error(f"[{datetime.now()}] Error during text extraction for material {material_id}: {e}")
                # Fallback to just title/description

        # Final check for text content before vectorization
        if not extracted_text.strip():
            extracted_text = db_material.title + " " + (db_material.description or "")  # Ensure some text exists

        # Ensure extracted text doesn't exceed Milvus VARCHAR limit
        extracted_text = extracted_text[:65000]

        # 2. Text Vectorization
        if extracted_text.strip():
            vector = vectorize_text(extracted_text)

            # 3. Insert into Milvus
            entities_to_insert = [{
                "embedding": vector,
                "material_id": db_material.id,
                "course_id": db_material.course_id,
                "material_type": db_material.file_type.value,
                "title": db_material.title,
                "source_text": extracted_text
            }]
            milvus_pks = insert_vectors_into_milvus(entities_to_insert)

            if milvus_pks:
                # 4. Update milvus_vector_id in PostgreSQL
                crud.update_material_milvus_vector_id(db, db_material.id, str(milvus_pks[0]))
                crud.update_material_extracted_text_content(db, db_material.id, extracted_text)
                logger.info(
                    f"[{datetime.now()}] Successfully vectorized and inserted material {material_id} into Milvus (PK: {milvus_pks[0]}).")
            else:
                logger.error(f"[{datetime.now()}] Failed to insert material {material_id} into Milvus, no PK returned.")
        else:
            logger.warning(
                f"[{datetime.now()}] No meaningful text content for vectorization for material {material_id}. Skipping Milvus insertion.")

    except Exception as e:
        logger.exception(f"[{datetime.now()}] Critical error during vectorization for material {material_id}: {e}")
    finally:
        db.close()


@router.post("/upload-credentials", response_model=PresignedUploadUrlResponse)
async def get_upload_credentials(
        course_id: int = Query(..., gt=0),
        original_filename: str = Query(..., min_length=1, max_length=255),
        file_type: MaterialType = Query(...),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取 Minio 预签名上传 URL。
    前端调用此接口获取 URL 后，直接将文件 PUT 到 Minio。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        logger.warning(f"Upload credentials request: Course {course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        logger.warning(f"Upload credentials request: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="Not enough permissions to upload materials for this course.")

    if not os.path.splitext(original_filename)[1]:
        raise BadRequestError(detail="Original filename must include a file extension.", code="INVALID_FILENAME")

    object_name = generate_minio_object_name(course_id, original_filename, file_type)

    try:
        upload_url = get_presigned_upload_url(object_name, expiry_seconds=3600)
        logger.info(f"Generated presigned upload URL for course {course_id}, object {object_name}.")
        return PresignedUploadUrlResponse(
            upload_url=upload_url,
            object_name=object_name,
            expires_in_seconds=3600
        )
    except Exception as e:
        logger.error(f"Minio error generating upload URL for object {object_name}: {e}")
        raise InternalServerError(detail=f"Failed to generate upload URL: {e}")


@router.post("/{course_id}", response_model=MaterialInDB, status_code=status.HTTP_201_CREATED)
async def create_new_material(
        material_data: MaterialUploadedConfirmation,
        background_tasks: BackgroundTasks,
        course_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 创建教学资料记录。
    此接口在前端已将文件上传到 Minio 后调用，后端只负责记录文件元数据到数据库。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        logger.warning(f"Create material request: Course {course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        logger.warning(f"Create material request: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="Not enough permissions to create materials for this course.")

    try:
        if not file_exists_in_minio(material_data.file_path_minio):
            logger.warning(f"Create material failed: File '{material_data.file_path_minio}' not found in Minio.")
            raise BadRequestError(
                detail="Uploaded file not found in storage. Please ensure file was uploaded successfully to Minio.",
                code="FILE_NOT_FOUND_IN_STORAGE")
    except Exception as e:
        logger.error(f"Minio error checking file existence for '{material_data.file_path_minio}': {e}")
        raise InternalServerError(detail=f"Storage check failed: {e}")

    expected_path_prefix = f"courses/{course_id}/{material_data.file_type.value}/"
    if not material_data.file_path_minio.startswith(expected_path_prefix):
        logger.warning(
            f"Create material failed: Invalid Minio path format for '{material_data.file_path_minio}'. Expected prefix: '{expected_path_prefix}'")
        raise BadRequestError(
            detail="Invalid file path format in storage. Please use the path provided by upload credentials.",
            code="INVALID_STORAGE_PATH")

    db_material = crud.create_material(
        db=db,
        material_data=MaterialCreate(**material_data.dict()),
        course_id=course_id,
        created_by_user_id=current_user.id
    )
    logger.info(f"Material '{db_material.title}' (ID: {db_material.id}) created successfully for course {course_id}.")

    background_tasks.add_task(
        process_material_for_vectorization_async,
        db_material.id,
        db_material.file_path_minio,
        db_material.file_type
    )

    return db_material


@router.get("/{material_id}/download-url", response_model=PresignedDownloadUrlResponse)
async def get_material_download_url(
        material_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生/教师端] 获取教学资料的 Minio 预签名下载 URL。
    """
    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material:
        logger.warning(f"Download URL request: Material {material_id} not found.")
        raise NotFoundError(detail="Material not found.")

    db_course = db_material.course
    if not db_course:
        logger.error(f"Material {material_id} has no associated course. Data integrity issue.")
        raise InternalServerError(detail="Material's course not found. Data integrity issue.")

    # Permission check: student must be in an associated class, or user is course teacher/admin
    has_access = False
    if current_user.role == UserRole.student:
        student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
            ClassStudent.student_id == current_user.id).distinct().all()
        student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}
        if db_course.teacher_id in student_class_teacher_ids:
            has_access = True
    elif current_user.role in [UserRole.teacher, UserRole.admin]:
        if current_user.id == db_course.teacher_id or current_user.role == UserRole.admin:
            has_access = True

    if not has_access:
        logger.warning(f"Download URL request: User {current_user.id} not authorized for material {material_id}.")
        raise ForbiddenError(detail="You do not have access to download this material.")

    try:
        download_url = get_presigned_download_url(db_material.file_path_minio, expiry_seconds=3600)
        logger.info(f"Generated presigned download URL for material {material_id}.")
        return PresignedDownloadUrlResponse(
            download_url=download_url,
            expires_in_seconds=3600
        )
    except Exception as e:
        logger.error(f"Minio error generating download URL for object {db_material.file_path_minio}: {e}")
        raise InternalServerError(detail=f"Failed to generate download URL: {e}")


@router.delete("/{material_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_material_by_id(
        material_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除教学资料记录及其在 Minio 中的文件。
    """
    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material:
        raise NotFoundError(detail="Material not found.")

    if db_material.created_by_user_id != current_user.id and db_material.course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this material")

    try:
        delete_file_from_minio(db_material.file_path_minio)
        logger.info(f"Successfully deleted file '{db_material.file_path_minio}' from Minio.")
    except Exception as e:
        logger.error(f"Warning: Could not delete file from Minio for material {material_id}: {e}")
        # In production, you might want a retry mechanism or manual intervention here.
        # For now, we continue to delete DB record even if Minio delete fails, logging the warning.
        raise InternalServerError(detail=f"Failed to delete file from storage: {e}")

    deleted_material = crud.delete_material(db, material_id)
    if not deleted_material:
        raise InternalServerError(detail="Failed to delete material record from database.")
    logger.info(f"Material {material_id} deleted successfully.")

    return


@router.get("/course/{course_id}", response_model=List[MaterialInDB])
async def get_materials_for_course(
        course_id: int = Path(..., gt=0),
        skip: int = 0,
        limit: int = 100,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生/教师端] 获取某个课程下的所有教学资料列表。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    # Permission check: student must be in an associated class, or user is course teacher/admin
    has_access = False
    if current_user.role == UserRole.student:
        student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
            ClassStudent.student_id == current_user.id).distinct().all()
        student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}
        if db_course.teacher_id in student_class_teacher_ids:
            has_access = True
    elif current_user.role in [UserRole.teacher, UserRole.admin]:
        if current_user.id == db_course.teacher_id or current_user.role == UserRole.admin:
            has_access = True

    if not has_access:
        raise ForbiddenError(detail="You are not enrolled in this course or do not have access.")

    materials = crud.get_materials_by_course_id(db, course_id=course_id, skip=skip, limit=limit)
    return materials


@router.put("/{material_id}", response_model=MaterialInDB)
async def update_material_info(
        material_update: MaterialUpdate,
        material_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新教学资料的元数据（标题、描述等）。
    """
    db_material = crud.get_material_by_id(db, material_id=material_id)
    if not db_material:
        raise NotFoundError(detail="Material not found.")

    if db_material.created_by_user_id != current_user.id and db_material.course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this material.")

    updated_material = crud.update_material(db, db_material, material_update)
    logger.info(f"Material {material_id} updated by user {current_user.id}.")
    return updated_material


@router.post("/search", response_model=List[MaterialSearchResult])
async def search_materials(
        search_query: MaterialSearchQuery,
        current_user: User = Depends(get_current_active_user),
        db: Session = Depends(get_db)
):
    """
    [学生/教师端] 根据语义查询搜索教学资料。
    """
    try:
        query_vector = vectorize_text(search_query.query_text)

        milvus_expr = None
        if search_query.course_id:
            db_course = crud.get_course_by_id(db, course_id=search_query.course_id)
            if not db_course:
                raise NotFoundError(detail="Course not found for specified search.")

            # Permission check for searching within a specific course
            has_access_to_course = False
            if current_user.role == UserRole.student:
                student_class_teacher_ids = db.query(Class.teacher_id).join(ClassStudent).filter(
                    ClassStudent.student_id == current_user.id).distinct().all()
                student_class_teacher_ids = {t[0] for t in student_class_teacher_ids}
                if db_course.teacher_id in student_class_teacher_ids:
                    has_access_to_course = True
            elif current_user.role in [UserRole.teacher, UserRole.admin]:
                if current_user.id == db_course.teacher_id or current_user.role == UserRole.admin:
                    has_access_to_course = True

            if not has_access_to_course:
                raise ForbiddenError(detail="You do not have access to search within this course.")

            milvus_expr = f"course_id == {search_query.course_id}"

        milvus_results = search_milvus(query_vector, top_k=search_query.top_k, expr=milvus_expr)

        material_ids = [res['material_id'] for res in milvus_results]

        pg_materials = db.query(Material).filter(Material.id.in_(material_ids)).all()
        pg_materials_map = {m.id: m for m in pg_materials}

        final_results = []
        for res in milvus_results:
            pg_material = pg_materials_map.get(res['material_id'])
            if pg_material:
                final_results.append(MaterialSearchResult(
                    id=res['id'],
                    distance=res['distance'],
                    material_id=pg_material.id,
                    course_id=pg_material.course_id,
                    title=pg_material.title,
                    material_type=pg_material.file_type,
                ))
        logger.info(
            f"User {current_user.id} performed search for '{search_query.query_text}', found {len(final_results)} results.")
        return final_results

    except Exception as e:
        logger.error(f"Error during material search for user {current_user.id}: {e}")
        raise InternalServerError(detail="An error occurred during search.")