from fastapi import APIRouter, Depends, status, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import json
import logging

from app.database import get_db
from app.schemas import (
    QuizCreate, QuizUpdate, QuizInDB, QuestionCreate, QuestionUpdate, QuestionInDB,
    StudentAttemptInDB, GradeShortAnswerRequest, GradedQuestion
)
from app import crud
from app.auth.dependencies import get_current_teacher
from app.models import User, UserRole, QuizQuestion, QuestionType, Quiz  # Import Quiz for typing
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/quizzes", tags=["Quizzes"])


# --- 教师端：测验/作业管理 ---

@router.post("/", response_model=QuizInDB, status_code=status.HTTP_201_CREATED)
async def create_new_quiz(
        quiz_data: QuizCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 创建新测验或作业。
    """
    db_course = crud.get_course_by_id(db, course_id=quiz_data.course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Course not owned by current teacher.")

    if quiz_data.due_date and quiz_data.due_date <= datetime.now(quiz_data.due_date.tzinfo):
        raise BadRequestError(detail="Due date must be in the future.")

    db_quiz = crud.create_quiz(db=db, quiz_data=quiz_data, created_by_user_id=current_user.id)
    logger.info(f"Teacher {current_user.id} created quiz '{db_quiz.title}' (ID: {db_quiz.id}).")
    return db_quiz


@router.get("/course/{course_id}", response_model=List[QuizInDB])
async def get_quizzes_in_course(
        course_id: int = Path(..., gt=0),
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取某个课程下的所有测验/作业列表。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Course not owned by current teacher.")

    quizzes = crud.get_quizzes_by_course_id(db, course_id=course_id, skip=skip, limit=limit)
    return quizzes


@router.get("/{quiz_id}", response_model=QuizInDB)
async def get_quiz(
        quiz_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取测验/作业详情（不包含问题）。
    """
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this quiz.")

    return db_quiz


@router.put("/{quiz_id}", response_model=QuizInDB)
async def update_quiz_info(
        quiz_update: QuizUpdate,
        quiz_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新测验/作业信息。
    """
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this quiz.")

    if quiz_update.due_date and quiz_update.due_date <= datetime.now(quiz_update.due_date.tzinfo):
        raise BadRequestError(detail="Due date must be in the future.")

    updated_quiz = crud.update_quiz(db, db_quiz, quiz_update)
    logger.info(f"User {current_user.id} updated quiz {quiz_id}.")
    return updated_quiz


@router.delete("/{quiz_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_quiz_by_id(
        quiz_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除测验/作业及其所有关联的问题和学生尝试。
    """
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this quiz.")

    deleted_quiz = crud.delete_quiz(db, quiz_id)
    if not deleted_quiz:
        raise InternalServerError(detail="Failed to delete quiz.")
    logger.info(f"User {current_user.id} deleted quiz {quiz_id}.")
    return


# --- 教师端：测验问题管理 ---

@router.post("/{quiz_id}/questions", response_model=QuestionInDB, status_code=status.HTTP_201_CREATED)
async def add_question_to_quiz(
        question_data: QuestionCreate,
        quiz_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 为指定测验/作业添加一个问题。
    """
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to add questions to this quiz.")

    db_question = crud.create_question(db=db, question_data=question_data, quiz_id=quiz_id)
    logger.info(f"Teacher {current_user.id} added question {db_question.id} to quiz {quiz_id}.")
    return db_question


@router.get("/{quiz_id}/questions", response_model=List[QuestionInDB])
async def get_questions_for_quiz(
        quiz_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取测验/作业的所有问题列表。
    """
    db_quiz = crud.get_quiz_with_questions(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view questions for this quiz.")

    questions_data = []
    for q in db_quiz.questions:
        q_dict = q.__dict__
        if q.options:
            q_dict['options'] = json.loads(q.options)
        if q.correct_answer:
            q_dict['correct_answer'] = json.loads(q.correct_answer)
        questions_data.append(QuestionInDB.parse_obj(q_dict))
    return questions_data


@router.get("/questions/{question_id}", response_model=QuestionInDB)
async def get_quiz_question(
        question_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取单个测验问题详情。
    """
    db_question = crud.get_question_by_id(db, question_id=question_id)
    if not db_question:
        raise NotFoundError(detail="Question not found.")

    # Check if teacher owns the quiz this question belongs to
    if not db_question.quiz or db_question.quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this question.")

    q_dict = db_question.__dict__
    if db_question.options:
        q_dict['options'] = json.loads(db_question.options)
    if db_question.correct_answer:
        q_dict['correct_answer'] = json.loads(db_question.correct_answer)
    return QuestionInDB.parse_obj(q_dict)


@router.put("/questions/{question_id}", response_model=QuestionInDB)
async def update_quiz_question(
        question_update: QuestionUpdate,
        question_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新测验问题。
    """
    db_question = crud.get_question_by_id(db, question_id=question_id)
    if not db_question:
        raise NotFoundError(detail="Question not found.")

    if not db_question.quiz or db_question.quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to update this question.")

    updated_question = crud.update_question(db, db_question, question_update)
    logger.info(f"Teacher {current_user.id} updated question {question_id}.")
    return updated_question


@router.delete("/questions/{question_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_quiz_question(
        question_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除测验问题。
    """
    db_question = crud.get_question_by_id(db, question_id=question_id)
    if not db_question:
        raise NotFoundError(detail="Question not found.")

    if not db_question.quiz or db_question.quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to delete this question.")

    deleted_question = crud.delete_question(db, question_id)
    if not deleted_question:
        raise InternalServerError(detail="Failed to delete question.")
    logger.info(f"Teacher {current_user.id} deleted question {question_id}.")
    return


# --- 教师端：学生测验尝试与批阅 ---

@router.get("/{quiz_id}/attempts", response_model=List[StudentAttemptInDB])
async def get_all_attempts_for_quiz(
        quiz_id: int = Path(..., gt=0),
        skip: int = Query(0, ge=0),
        limit: int = Query(100, gt=0, le=200),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取某个测验/作业的所有学生尝试列表。
    """
    db_quiz = crud.get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view attempts for this quiz.")

    attempts = crud.get_student_quiz_attempts_for_quiz(db, quiz_id=quiz_id, skip=skip, limit=limit)

    for attempt in attempts:
        if attempt.submitted_answers:
            attempt.submitted_answers = json.loads(attempt.submitted_answers)
    return attempts


@router.get("/attempts/{attempt_id}", response_model=StudentAttemptDetailForTeacher)
async def get_attempt_detail_for_teacher(
        attempt_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取学生某个测验尝试的详细信息，包含问题和学生作答。
    """
    db_attempt = crud.get_student_quiz_attempt_with_questions(db, attempt_id=attempt_id)
    if not db_attempt:
        raise NotFoundError(detail="Student attempt not found.")

    db_quiz = db_attempt.quiz
    if not db_quiz or db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this attempt.")

    db_student = db_attempt.student

    quiz_questions = db_quiz.questions
    questions_map = {q.id: q for q in quiz_questions}

    submitted_answers_dict = {}
    if db_attempt.submitted_answers:
        submitted_answers_dict = json.loads(db_attempt.submitted_answers)

    graded_questions: List[GradedQuestion] = []
    for question in quiz_questions:
        submitted_answer = submitted_answers_dict.get(str(question.id))

        correct_ans = None
        if question.correct_answer:
            try:
                correct_ans = json.loads(question.correct_answer)
            except json.JSONDecodeError:
                logger.error(f"Error parsing correct_answer for question {question.id}: {question.correct_answer}")

        is_correct = None
        auto_score = 0

        if question.question_type == QuestionType.single_choice:
            is_correct = (submitted_answer == correct_ans)
            if is_correct: auto_score = question.score
        elif question.question_type == QuestionType.multiple_choice:
            submitted_list = sorted(submitted_answer) if isinstance(submitted_answer, list) else []
            correct_list = sorted(correct_ans) if isinstance(correct_ans, list) else []
            is_correct = (submitted_list == correct_list)
            if is_correct: auto_score = question.score
        elif question.question_type == QuestionType.fill_in_blank:
            is_correct = (str(submitted_answer).strip().lower() == str(correct_ans).strip().lower())
            if is_correct: auto_score = question.score

        graded_questions.append(GradedQuestion(
            question_id=question.id,
            question_text=question.question_text,
            question_type=question.question_type,
            submitted_answer=submitted_answer,
            correct_answer=correct_ans,
            score=auto_score if question.question_type != QuestionType.short_answer else None,
            feedback=None,
            is_correct=is_correct
        ))

    response_data = StudentAttemptDetailForTeacher(
        id=db_attempt.id,
        student_id=db_attempt.student_id,
        quiz_id=db_attempt.quiz_id,
        score=db_attempt.score,
        submitted_answers=submitted_answers_dict,
        start_time=db_attempt.start_time,
        submit_time=db_attempt.submit_time,
        is_completed=db_attempt.is_completed,
        created_at=db_attempt.created_at,
        updated_at=db_attempt.updated_at,
        student_username=db_student.username,
        quiz_title=db_quiz.title,
        graded_questions=graded_questions
    )
    return response_data


@router.put("/attempts/{attempt_id}/questions/{question_id}/grade", response_model=StudentAttemptInDB)
async def grade_short_answer_question(
        grade_request: GradeShortAnswerRequest,
        attempt_id: int = Path(..., gt=0),
        question_id: int = Path(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 批阅学生测验尝试中的某个主观题。
    此操作会更新该问题在尝试中的得分，并重新计算总分。
    """
    db_attempt = crud.get_student_quiz_attempt_by_id(db, attempt_id=attempt_id)
    if not db_attempt:
        raise NotFoundError(detail="Student attempt not found.")

    db_quiz = db_attempt.quiz
    if not db_quiz or db_quiz.created_by_user_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to grade this attempt.")

    db_question = crud.get_question_by_id(db, question_id=question_id)
    if not db_question or db_question.quiz_id != db_quiz.id:
        raise NotFoundError(detail="Question not found in this quiz.")

    if db_question.question_type != QuestionType.short_answer:
        raise BadRequestError(detail="Only short answer questions can be graded manually via this endpoint.",
                              code="INVALID_QUESTION_TYPE")

    updated_attempt = crud.update_attempt_score_and_feedback(db, db_attempt, question_id, grade_request.score,
                                                             grade_request.feedback)

    if updated_attempt.submitted_answers:
        updated_attempt.submitted_answers = json.loads(updated_attempt.submitted_answers)
    logger.info(f"Teacher {current_user.id} graded question {question_id} in attempt {attempt_id}.")
    return updated_attempt