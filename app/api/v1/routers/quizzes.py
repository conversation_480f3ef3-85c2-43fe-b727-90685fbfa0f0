from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import Field
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, Union
import json  # 用于处理 JSON 字符串
import logging

from app.database import get_db
from app.schemas import (
    QuizCreate, QuizUpdate, QuizInDB, QuestionCreate, QuestionUpdate, QuestionInDB,
    StudentAttemptCreate, StudentAttemptInDB,
    # 确保 StudentAttemptDetailForTeacher 和 GradedQuestion 被正确导入
    # 它们的定义在 schemas.py 中也必须正确排序
    StudentAttemptDetailForTeacher, GradedQuestion,
    GradeShortAnswerRequest, UserPublic
)
from app.crud import (
    create_quiz, get_quiz_by_id, get_quizzes_by_course_id, update_quiz, delete_quiz,
    create_question, get_question_by_id, update_question, delete_question,
    create_student_quiz_attempt, get_student_quiz_attempts_for_quiz,
    get_quiz_with_questions, get_user_by_id, update_attempt_score_and_feedback,
    get_course_by_id, get_student_quiz_attempt_by_id
)
from app.auth.dependencies import get_current_teacher, get_current_active_user
from app.models import User, UserRole, QuizQuestion, QuestionType, Quiz
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/quizzes", tags=["Quizzes"])


# --- 教师端：测验/作业管理 ---

@router.post("/", response_model=QuizInDB, status_code=status.HTTP_201_CREATED)
async def create_new_quiz(
        quiz_data: QuizCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 创建新测验或作业。
    """
    db_course = get_course_by_id(db, course_id=quiz_data.course_id)
    if not db_course:
        logger.warning(f"Quiz creation failed: Course {quiz_data.course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        logger.warning(f"Quiz creation failed: User {current_user.id} not authorized for course {quiz_data.course_id}.")
        raise ForbiddenError(detail="Not enough permissions to create quizzes for this course.")

    db_quiz = create_quiz(db=db, quiz_data=quiz_data, created_by_user_id=current_user.id)
    logger.info(
        f"Quiz '{db_quiz.title}' (ID: {db_quiz.id}) created by teacher {current_user.id} for course {quiz_data.course_id}.")
    return db_quiz


@router.get("/course/{course_id}", response_model=List[QuizInDB])
async def get_quizzes_in_course(
        course_id: int = Field(..., gt=0),
        skip: int = 0,
        limit: int = 100,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取某个课程下的所有测验/作业列表。
    """
    db_course = get_course_by_id(db, course_id=course_id)
    if not db_course:
        logger.warning(f"Get quizzes in course failed: Course {course_id} not found.")
        raise NotFoundError(detail="Course not found.")

    if db_course.teacher_id != current_user.id:
        logger.warning(f"Get quizzes in course failed: User {current_user.id} not authorized for course {course_id}.")
        raise ForbiddenError(detail="Not enough permissions to view quizzes for this course.")

    quizzes = get_quizzes_by_course_id(db, course_id=course_id, skip=skip, limit=limit)
    return quizzes


@router.get("/{quiz_id}", response_model=QuizInDB)  # 可以返回包含问题列表的更详细的 Schema
async def get_quiz(
        quiz_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取测验/作业详情（不包含问题）。
    """
    db_quiz = get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Get quiz failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Get quiz failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to view this quiz.")

    return db_quiz


@router.put("/{quiz_id}", response_model=QuizInDB)
async def update_quiz_info(
        quiz_id: int = Field(..., gt=0),
        quiz_update: QuizUpdate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新测验/作业信息。
    """
    db_quiz = get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Update quiz failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Update quiz failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to update this quiz.")

    updated_quiz = update_quiz(db, db_quiz, quiz_update)
    logger.info(f"Quiz '{updated_quiz.title}' (ID: {updated_quiz.id}) updated by teacher {current_user.id}.")
    return updated_quiz


@router.delete("/{quiz_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_quiz_by_id(
        quiz_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除测验/作业及其所有关联的问题和学生尝试。
    """
    db_quiz = get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Delete quiz failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Delete quiz failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to delete this quiz.")

    deleted_quiz = delete_quiz(db, quiz_id)
    if not deleted_quiz:
        logger.error(f"Failed to delete quiz {quiz_id} for teacher {current_user.id}.")
        raise InternalServerError(detail="Failed to delete quiz.")
    logger.info(f"Quiz {quiz_id} deleted by teacher {current_user.id}.")

    return


# --- 教师端：测验问题管理 ---

@router.post("/{quiz_id}/questions", response_model=QuestionInDB, status_code=status.HTTP_201_CREATED)
async def add_question_to_quiz(
        quiz_id: int = Field(..., gt=0),
        question_data: QuestionCreate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 为指定测验/作业添加一个问题。
    """
    db_quiz = get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Add question failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Add question failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to add questions to this quiz.")

    # 验证 correct_answer 格式与 question_type 匹配
    if question_data.question_type in [QuestionType.single_choice, QuestionType.fill_in_blank] and not isinstance(
            question_data.correct_answer, str):
        raise BadRequestError(
            detail=f"{question_data.question_type.value} question must have a string as correct_answer.",
            code="INVALID_CORRECT_ANSWER_FORMAT")
    elif question_data.question_type == QuestionType.multiple_choice and not isinstance(question_data.correct_answer,
                                                                                        list):
        raise BadRequestError(detail="Multiple choice question must have a list as correct_answer.",
                              code="INVALID_CORRECT_ANSWER_FORMAT")
    elif question_data.question_type == QuestionType.short_answer and question_data.correct_answer is not None:
        raise BadRequestError(detail="Short answer question should not have a predefined correct_answer.",
                              code="INVALID_CORRECT_ANSWER_FORMAT")

    db_question = create_question(db=db, question_data=question_data, quiz_id=quiz_id)
    logger.info(
        f"Question '{db_question.question_text[:50]}...' (ID: {db_question.id}) added to quiz {quiz_id} by teacher {current_user.id}.")
    return db_question


@router.get("/{quiz_id}/questions", response_model=List[QuestionInDB])
async def get_questions_for_quiz(
        quiz_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取测验/作业的所有问题列表。
    """
    db_quiz = get_quiz_with_questions(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Get questions for quiz failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Get questions for quiz failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to view questions for this quiz.")

    questions_data = []
    for q in db_quiz.questions:
        # Pydantic ORM mode handles parsing JSON strings automatically if field type is set to dict/list/Any
        questions_data.append(QuestionInDB.from_orm(q))
    return questions_data


@router.get("/questions/{question_id}", response_model=QuestionInDB)
async def get_quiz_question(
        question_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取单个测验问题详情。
    """
    db_question = get_question_by_id(db, question_id=question_id)
    if not db_question:
        logger.warning(f"Get question failed: Question {question_id} not found.")
        raise NotFoundError(detail="Question not found.")

    if db_question.quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Get question failed: User {current_user.id} not authorized for question {question_id}.")
        raise ForbiddenError(detail="Not enough permissions to view this question.")

    # Pydantic ORM mode handles parsing JSON strings automatically
    return QuestionInDB.from_orm(db_question)


@router.put("/questions/{question_id}", response_model=QuestionInDB)
async def update_quiz_question(
        question_id: int = Field(..., gt=0),
        question_update: QuestionUpdate,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 更新测验问题。
    """
    db_question = get_question_by_id(db, question_id=question_id)
    if not db_question:
        logger.warning(f"Update question failed: Question {question_id} not found.")
        raise NotFoundError(detail="Question not found.")

    if db_question.quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Update question failed: User {current_user.id} not authorized for question {question_id}.")
        raise ForbiddenError(detail="Not enough permissions to update this question.")

    # 验证 correct_answer 格式与 question_type 匹配（更新时同样需要验证）
    if question_update.question_type is not None:  # 仅当 question_type 被更新时才验证
        if question_update.question_type in [QuestionType.single_choice, QuestionType.fill_in_blank] and not isinstance(
                question_update.correct_answer, (str, type(None))):
            raise BadRequestError(
                detail=f"{question_update.question_type.value} question must have a string as correct_answer or None.",
                code="INVALID_CORRECT_ANSWER_FORMAT")
        elif question_update.question_type == QuestionType.multiple_choice and not isinstance(
                question_update.correct_answer, (list, type(None))):
            raise BadRequestError(detail="Multiple choice question must have a list as correct_answer or None.",
                                  code="INVALID_CORRECT_ANSWER_FORMAT")
        elif question_update.question_type == QuestionType.short_answer and question_update.correct_answer is not None:
            raise BadRequestError(detail="Short answer question should not have a predefined correct_answer.",
                                  code="INVALID_CORRECT_ANSWER_FORMAT")

    updated_question = update_question(db, db_question, question_update)
    logger.info(f"Question {question_id} updated by teacher {current_user.id}.")
    return updated_question


@router.delete("/questions/{question_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_quiz_question(
        question_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 删除测验问题。
    """
    db_question = get_question_by_id(db, question_id=question_id)
    if not db_question:
        logger.warning(f"Delete question failed: Question {question_id} not found.")
        raise NotFoundError(detail="Question not found.")

    if db_question.quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Delete question failed: User {current_user.id} not authorized for question {question_id}.")
        raise ForbiddenError(detail="Not enough permissions to delete this question.")

    deleted_question = delete_question(db, question_id)
    if not deleted_question:
        logger.error(f"Failed to delete question {question_id} for teacher {current_user.id}.")
        raise InternalServerError(detail="Failed to delete question.")
    logger.info(f"Question {question_id} deleted by teacher {current_user.id}.")

    return


# --- 教师端：学生测验尝试与批阅 ---

@router.get("/attempts/{quiz_id}", response_model=List[StudentAttemptInDB])
async def get_all_attempts_for_quiz(
        quiz_id: int = Field(..., gt=0),
        skip: int = 0,
        limit: int = 100,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取某个测验/作业的所有学生尝试列表。
    """
    db_quiz = get_quiz_by_id(db, quiz_id=quiz_id)
    if not db_quiz:
        logger.warning(f"Get attempts for quiz failed: Quiz {quiz_id} not found.")
        raise NotFoundError(detail="Quiz not found.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Get attempts for quiz failed: User {current_user.id} not authorized for quiz {quiz_id}.")
        raise ForbiddenError(detail="Not enough permissions to view attempts for this quiz.")

    attempts = get_student_quiz_attempts_for_quiz(db, quiz_id=quiz_id, skip=skip, limit=limit)

    # Pydantic ORM mode handles parsing JSON strings automatically
    return [StudentAttemptInDB.from_orm(attempt) for attempt in attempts]


@router.get("/attempts/{attempt_id}", response_model=StudentAttemptDetailForTeacher)
async def get_attempt_detail_for_teacher(
        attempt_id: int = Field(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 获取学生某个测验尝试的详细信息，包含问题和学生作答，以及自动批改结果。
    """
    db_attempt = get_student_quiz_attempt_by_id(db, attempt_id=attempt_id)
    if not db_attempt:
        logger.warning(f"Get attempt detail failed: Student attempt {attempt_id} not found.")
        raise NotFoundError(detail="Student attempt not found.")

    db_quiz = db_attempt.quiz  # ORM 关系自动加载
    if not db_quiz:
        logger.error(f"Attempt {attempt_id} quiz not found. Data inconsistency.")
        raise InternalServerError(detail="Associated quiz not found for this attempt.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Get attempt detail failed: User {current_user.id} not authorized for quiz {db_quiz.id}.")
        raise ForbiddenError(detail="Not enough permissions to view this attempt.")

    db_student = db_attempt.student  # ORM 关系自动加载
    if not db_student:
        logger.error(f"Attempt {attempt_id} student not found. Data inconsistency.")
        raise InternalServerError(detail="Associated student not found for this attempt.")

    # 加载测验的所有问题
    quiz_questions = db_quiz.questions  # 假设 Quiz 模型已配置 questions 关系
    questions_map = {q.id: q for q in quiz_questions}

    # 解析学生提交的答案
    submitted_answers_dict = {}
    if db_attempt.submitted_answers:
        submitted_answers_dict = json.loads(db_attempt.submitted_answers)

    graded_questions: List[GradedQuestion] = []
    for question in quiz_questions:
        submitted_answer = submitted_answers_dict.get(str(question.id))
        correct_ans = json.loads(question.correct_answer) if question.correct_answer else None

        is_correct = None
        auto_score = 0

        # 自动批改逻辑
        if question.question_type == QuestionType.single_choice:
            is_correct = (submitted_answer == correct_ans)
            if is_correct: auto_score = question.score
        elif question.question_type == QuestionType.multiple_choice:
            submitted_list = sorted(submitted_answer) if isinstance(submitted_answer, list) else []
            correct_list = sorted(correct_ans) if isinstance(correct_ans, list) else []
            is_correct = (submitted_list == correct_list)
            if is_correct: auto_score = question.score
        elif question.question_type == QuestionType.fill_in_blank:
            is_correct = (str(submitted_answer).strip().lower() == str(correct_ans).strip().lower())
            if is_correct: auto_score = question.score

        # 短答题不在此处自动批改，score 留空或为0，is_correct 留空

        graded_questions.append(GradedQuestion(
            question_id=question.id,
            question_text=question.question_text,
            question_type=question.question_type,
            submitted_answer=submitted_answer,
            correct_answer=correct_ans,
            score=auto_score if question.question_type != QuestionType.short_answer else None,  # 初始分数
            feedback=None,  # 初始无反馈
            is_correct=is_correct
        ))

    response_data = StudentAttemptDetailForTeacher(
        id=db_attempt.id,
        student_id=db_attempt.student_id,
        quiz_id=db_attempt.quiz_id,
        score=db_attempt.score,
        submitted_answers=submitted_answers_dict,  # Pydantic ORM mode will parse this
        start_time=db_attempt.start_time,
        submit_time=db_attempt.submit_time,
        is_completed=db_attempt.is_completed,
        created_at=db_attempt.created_at,
        updated_at=db_attempt.updated_at,
        student_username=db_student.username,
        quiz_title=db_quiz.title,
        graded_questions=graded_questions
    )
    return response_data


@router.put("/attempts/{attempt_id}/questions/{question_id}/grade", response_model=StudentAttemptInDB)
async def grade_short_answer_question(
        attempt_id: int = Field(..., gt=0),
        question_id: int = Field(..., gt=0),
        grade_request: GradeShortAnswerRequest,
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端] 批阅学生测验尝试中的某个主观题。
    此操作会更新该问题在尝试中的得分，并重新计算总分。
    """
    db_attempt = get_student_quiz_attempt_by_id(db, attempt_id=attempt_id)
    if not db_attempt:
        logger.warning(f"Grade attempt failed: Student attempt {attempt_id} not found.")
        raise NotFoundError(detail="Student attempt not found.")

    db_quiz = db_attempt.quiz
    if not db_quiz:
        logger.error(f"Attempt {attempt_id} quiz not found during grading. Data inconsistency.")
        raise InternalServerError(detail="Associated quiz not found for this attempt.")

    if db_quiz.created_by_user_id != current_user.id and current_user.role != UserRole.admin:
        logger.warning(f"Grade attempt failed: User {current_user.id} not authorized for quiz {db_quiz.id}.")
        raise ForbiddenError(detail="Not enough permissions to grade this attempt.")

    db_question = get_question_by_id(db, question_id=question_id)
    if not db_question or db_question.quiz_id != db_quiz.id:
        logger.warning(f"Grade attempt failed: Question {question_id} not found in quiz {db_quiz.id}.")
        raise NotFoundError(detail="Question not found in this quiz.")

    if db_question.question_type != QuestionType.short_answer:
        raise BadRequestError(detail="Only short answer questions can be graded manually via this endpoint.",
                              code="INVALID_QUESTION_TYPE_FOR_GRADING")

    updated_attempt = update_attempt_score_and_feedback(db, db_attempt, question_id, grade_request.score,
                                                        grade_request.feedback)

    # Pydantic ORM mode handles JSON parsing for the return model
    logger.info(
        f"Teacher {current_user.id} graded question {question_id} in attempt {attempt_id} with score {grade_request.score}.")
    return StudentAttemptInDB.from_orm(updated_attempt)