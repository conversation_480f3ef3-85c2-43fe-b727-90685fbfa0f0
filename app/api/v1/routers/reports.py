from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date, timedelta
import logging

from app.database import get_db
from app.auth.dependencies import get_current_teacher  # Reports usually viewed by teachers/admins
from app.models import User, UserRole, Class, ClassStudent, Course, Material, StudentMaterialProgress, \
    StudentQuizAttempt, Quiz  # Import for type hinting/queries
from app.schemas import (
    DailyStudyDuration, CourseLearningReport, ClassLearningReport, StudentLearningOverview,
    MaterialCompletionRate, QuizScoreDistribution, UserPublic
)
from app import crud
from app.exceptions import BadRequestError, NotFoundError, ForbiddenError, InternalServerError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["Reports"])


@router.get("/students/{student_id}/daily_study_duration", response_model=List[DailyStudyDuration])
async def get_student_daily_report(
        student_id: int = Query(..., gt=0),
        start_date: date = Query(..., description="Start date for the report (YYYY-MM-DD)"),
        end_date: date = Query(..., description="End date for the report (YYYY-MM-DD)"),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端/管理员] 获取学生在指定日期范围内的每日学习时长。
    """
    db_student = crud.get_user_by_id(db, user_id=student_id)
    if not db_student or db_student.role != UserRole.student:
        raise NotFoundError(detail="Student not found.")

    # Permission check: teacher can only view reports of students in their classes
    if current_user.role == UserRole.teacher:
        is_student_of_current_teacher = db.query(ClassStudent).join(Class).filter(
            ClassStudent.student_id == student_id,
            Class.teacher_id == current_user.id
        ).first()
        if not is_student_of_current_teacher:
            raise ForbiddenError(detail="You do not have permission to view this student's report.")

    if start_date > end_date:
        raise BadRequestError(detail="Start date cannot be after end date.")

    # Ensure end_date includes the entire day
    end_date_inclusive = datetime.combine(end_date, datetime.max.time())

    results = crud.get_student_daily_study_duration(db, student_id,
                                                    datetime.combine(start_date, datetime.min.time()),
                                                    end_date_inclusive)

    report_data = []
    for d, duration in results:
        report_data.append(DailyStudyDuration(date=d, duration_seconds=duration))

    logger.info(f"User {current_user.id} fetched daily study duration for student {student_id}.")
    return report_data


@router.get("/courses/{course_id}", response_model=CourseLearningReport)
async def get_course_report(
        course_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端/管理员] 获取某个课程的学习报告。
    包括总学生数、平均完成度、资料完成率、测验成绩分布等。
    """
    db_course = crud.get_course_by_id(db, course_id=course_id)
    if not db_course:
        raise NotFoundError(detail="Course not found.")

    if current_user.role == UserRole.teacher and db_course.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this course's report.")

    students_in_course = crud.get_students_enrolled_in_course(db, course_id)
    total_students = len(students_in_course)

    total_materials_count = db.query(func.count(Material.id)).filter(Material.course_id == course_id).scalar()
    if total_materials_count == 0:
        average_completion_percentage = 0.0
    else:
        avg_progress_query = db.query(func.avg(StudentMaterialProgress.progress_percentage)).filter(
            StudentMaterialProgress.material_id == Material.id,
            Material.course_id == course_id
        )
        avg_progress_result = avg_progress_query.scalar()
        average_completion_percentage = round(float(avg_progress_result), 2) if avg_progress_result else 0.0

    material_completion_rates_data = crud.get_course_material_completion_rates(db, course_id)
    material_completion_rates_list = []
    for material, avg_percentage, completed_count in material_completion_rates_data:
        material_completion_rates_list.append(MaterialCompletionRate(
            material_id=material.id,
            material_title=material.title,
            completion_percentage=round(float(avg_percentage), 2) if avg_percentage is not None else 0.0,
            completed_students_count=completed_count if completed_count is not None else 0
        ))

    quiz_score_distribution_data = crud.get_quiz_score_distribution_for_course(db, course_id)

    logger.info(f"User {current_user.id} fetched report for course {course_id}.")
    return CourseLearningReport(
        course_id=db_course.id,
        course_title=db_course.title,
        total_students=total_students,
        average_completion_percentage=average_completion_percentage,
        material_completion_rates=material_completion_rates_list,
        quiz_score_distribution=quiz_score_distribution_data
    )


@router.get("/classes/{class_id}", response_model=ClassLearningReport)
async def get_class_report(
        class_id: int = Query(..., gt=0),
        current_user: User = Depends(get_current_teacher),
        db: Session = Depends(get_db)
):
    """
    [教师端/管理员] 获取某个班级的学习报告。
    包括总学生数、班级平均学习时长、班级平均测验成绩，以及每个学生的学习概览。
    """
    db_class = crud.get_class_by_id(db, class_id=class_id)
    if not db_class:
        raise NotFoundError(detail="Class not found.")

    if current_user.role == UserRole.teacher and db_class.teacher_id != current_user.id:
        raise ForbiddenError(detail="Not enough permissions to view this class's report.")

    teacher_name = db_class.teacher.full_name if db_class.teacher else "Unknown Teacher"

    students_in_class = crud.get_students_in_class(db, class_id)
    total_students = len(students_in_class)

    class_total_study_duration_result = db.query(func.sum(StudentMaterialProgress.total_view_duration_seconds)).join(
        User).join(ClassStudent).filter(
        ClassStudent.class_id == class_id
    ).scalar()
    average_class_study_duration_seconds = int(
        class_total_study_duration_result / total_students) if total_students > 0 and class_total_study_duration_result else 0

    class_avg_quiz_score_result = db.query(func.avg(StudentQuizAttempt.score)).join(User).join(ClassStudent).filter(
        ClassStudent.class_id == class_id,
        StudentQuizAttempt.is_completed == True,
        StudentQuizAttempt.score.isnot(None)
    ).scalar()
    average_class_quiz_score = round(float(class_avg_quiz_score_result), 2) if class_avg_quiz_score_result else None

    student_overviews_list: List[StudentLearningOverview] = []
    for student in students_in_class:
        student_stats = crud.get_student_overall_stats(db, student.id)
        student_overviews_list.append(StudentLearningOverview(
            student_id=student.id,
            student_name=student.full_name or student.username,
            total_courses_enrolled=student_stats.get('total_courses_enrolled', 0),
            completed_courses_count=student_stats.get('completed_courses_count', 0),
            average_course_progress=student_stats.get('average_course_progress', 0.0),
            total_study_duration_seconds=student_stats.get('total_study_duration_seconds', 0),
            average_quiz_score=student_stats.get('average_quiz_score')
        ))

    logger.info(f"User {current_user.id} fetched report for class {class_id}.")
    return ClassLearningReport(
        class_id=db_class.id,
        class_name=db_class.name,
        teacher_name=teacher_name,
        total_students=total_students,
        average_class_study_duration_seconds=average_class_study_duration_seconds,
        average_class_quiz_score=average_class_quiz_score,
        student_overviews=student_overviews_list
    )