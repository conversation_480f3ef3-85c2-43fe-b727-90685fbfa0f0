import os
from dotenv import load_dotenv
import logging
from logging.handlers import RotatingFileHandler

load_dotenv()  # 加载 .env 文件中的环境变量


class Settings:
    DATABASE_URL: str = os.getenv("DATABASE_URL")
    SECRET_KEY: str = os.getenv("SECRET_KEY")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    MINIO_ENDPOINT: str = os.getenv("MINIO_ENDPOINT")
    MINIO_ACCESS_KEY: str = os.getenv("MINIO_ACCESS_KEY")
    MINIO_SECRET_KEY: str = os.getenv("MINIO_SECRET_KEY")
    MINIO_BUCKET_NAME: str = os.getenv("MINIO_BUCKET_NAME")
    MINIO_SECURE: bool = os.getenv("MINIO_SECURE", "False").lower() in ('true', '1', 't')

    MILVUS_URI: str = os.getenv("MILVUS_URI")
    EMBEDDING_DIM: int = int(os.getenv("EMBEDDING_DIM", "384"))  # Ensure it's an integer

    # --- Logging Configuration ---
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO").upper()
    LOG_FILE_PATH: str = os.getenv("LOG_FILE_PATH", "/app/logs/app.log")  # Default path inside container
    LOG_MAX_BYTES: int = int(os.getenv("LOG_MAX_BYTES", "10485760"))  # 10 MB
    LOG_BACKUP_COUNT: int = int(os.getenv("LOG_BACKUP_COUNT", "5"))  # Keep 5 backup files


settings = Settings()


# --- Initialize Logger ---
def setup_logging():
    log_level = getattr(logging, settings.LOG_LEVEL, logging.INFO)

    # 获取根logger
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 移除已有的handlers，避免重复日志
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # 控制台输出 handler
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter('%(levelname)s:     %(asctime)s - %(name)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 文件输出 handler (RotatingFileHandler)
    # Ensure the log directory exists (handled by Dockerfile RUN mkdir)
    file_handler = RotatingFileHandler(
        settings.LOG_FILE_PATH,
        maxBytes=settings.LOG_MAX_BYTES,
        backupCount=settings.LOG_BACKUP_COUNT
    )
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # 设置 SQLAlchemy 和 Uvicorn 的日志级别
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("uvicorn.error").setLevel(logging.ERROR)
    logging.getLogger("minio").setLevel(logging.WARNING)
    logging.getLogger("pymilvus").setLevel(logging.WARNING)
    logging.getLogger("sentence_transformers").setLevel(
        logging.WARNING)  # This can be INFO for debugging model download

    # Filter out verbose warnings from some libraries if not critical
    logging.getLogger("httpx").setLevel(logging.WARNING)


# Ensure setup_logging is called when config.py is imported
setup_logging()