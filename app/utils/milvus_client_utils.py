# Stub implementation for development without <PERSON><PERSON><PERSON><PERSON> and sentence-transformers
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

# Mock sentence transformer model
class MockSentenceTransformer:
    def encode(self, texts, convert_to_tensor=False):
        # Return mock embeddings (384 dimensions)
        if isinstance(texts, str):
            texts = [texts]
        return [[0.1] * 384 for _ in texts]

_model = None

def get_sentence_transformer_model():
    """Get or load the sentence transformer model."""
    global _model
    if _model is None:
        logger.info("Mock AI: Loading mock sentence transformer model")
        _model = MockSentenceTransformer()
    return _model

def init_milvus():
    """Initialize Milvus connection and ensure Collection exists."""
    logger.info("Mock Milvus: Initialized mock connection")

def create_milvus_collection():
    """Create Milvus Collection and its Schema."""
    logger.info("Mock Milvus: Created mock collection")

def insert_vectors_into_milvus(entities: List[Dict[str, Any]]) -> List[int]:
    """Insert vectors and associated metadata into Milvus."""
    logger.info(f"Mock Milvus: Inserted {len(entities)} vectors")
    return list(range(len(entities)))

def search_milvus(query_vector: List[float], top_k: int = 10, expr: Optional[str] = None) -> List[Dict[str, Any]]:
    """Perform vector similarity search in Milvus."""
    logger.info(f"Mock Milvus: Searched for {top_k} similar vectors")
    return []

def delete_vectors_from_milvus(material_id: int):
    """Delete vectors associated with a material from Milvus."""
    logger.info(f"Mock Milvus: Deleted vectors for material {material_id}")

def generate_embeddings_for_text(text: str) -> List[float]:
    """Generate embeddings for given text using sentence transformer."""
    model = get_sentence_transformer_model()
    embeddings = model.encode([text])
    return embeddings[0]
