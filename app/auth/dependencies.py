from fastapi import Depends, HTTPException, status, WebSocketException, WebSocket
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session
from app.database import get_db
from app.auth.security import decode_access_token
from app import crud
from app.models import User, UserRole
from app.schemas import TokenData
import logging

logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    HTTP 认证依赖，用于常规 REST API。
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    payload = decode_access_token(token)
    if payload is None:
        logger.warning("JWT decode failed or payload is empty.")
        raise credentials_exception

    username: str = payload.get("sub")
    user_id: int = payload.get("user_id")
    role: str = payload.get("role")

    if username is None or user_id is None or role is None:
        logger.warning(f"Invalid token payload: missing username, user_id or role. Payload: {payload}")
        raise credentials_exception

    token_data = TokenData(username=username, user_id=user_id, role=UserRole(role))

    user = crud.get_user_by_id(db, user_id=token_data.user_id)
    if user is None:
        logger.warning(f"User with ID {token_data.user_id} from token not found in DB.")
        raise credentials_exception
    return user


async def get_current_websocket_user(websocket: WebSocket, token: Optional[str] = None,
                                     db: Session = Depends(get_db)) -> User:
    """
    WebSocket 认证依赖。从查询参数 'token' 获取 JWT token。
    """
    token = websocket.query_params.get("token")
    if not token:
        logger.warning("WebSocket authentication failed: Missing token in query params.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication token required")

    payload = decode_access_token(token)
    if payload is None:
        logger.warning("WebSocket authentication failed: Invalid token.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication token")

    username: str = payload.get("sub")
    user_id: int = payload.get("user_id")
    role: str = payload.get("role")

    if username is None or user_id is None or role is None:
        logger.warning(f"WebSocket authentication failed: Invalid token payload for user_id={user_id}.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid authentication token payload")

    token_data = TokenData(username=username, user_id=user_id, role=UserRole(role))

    user = crud.get_user_by_id(db, user_id=token_data.user_id)
    if user is None:
        logger.warning(f"WebSocket authentication failed: User with ID {token_data.user_id} not found.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="User not found")

    if not user.is_active:
        logger.warning(f"WebSocket authentication failed: Inactive user {user.username}.")
        raise WebSocketException(code=status.WS_1008_POLICY_VIOLATION, reason="User is inactive")

    return user


def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")
    return current_user


def get_current_teacher(current_user: User = Depends(get_current_active_user)) -> User:
    if current_user.role not in [UserRole.teacher, UserRole.admin]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions (Teacher or Admin required)"
        )
    return current_user


def get_current_admin(current_user: User = Depends(get_current_active_user)) -> User:
    if current_user.role != UserRole.admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions (Admin required)"
        )
    return current_user