import enum
from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Boolean, DateTime, Enum, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.schema import UniqueConstraint
from sqlalchemy.orm import relationship

from app.database import Base

# 枚举类型
class UserRole(enum.Enum):
    student = "student"
    teacher = "teacher"
    admin = "admin"

class MaterialType(enum.Enum):
    video = "video"
    image = "image"
    ppt = "ppt"
    pdf = "pdf"
    word = "word"
    audio = "audio"
    text = "text"

class QuizType(enum.Enum):
    quiz = "quiz"
    assignment = "assignment"

class QuestionType(enum.Enum):
    single_choice = "single_choice"
    multiple_choice = "multiple_choice"
    fill_in_blank = "fill_in_blank"
    short_answer = "short_answer"

class NotificationType(enum.Enum):
    system = "system"
    course = "course"
    class_ = "class" # Python keyword, so use class_
    personal = "personal"

# 用户表 (users) - 统一管理学生、教师、管理员
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100))
    role = Column(Enum(UserRole), nullable=False)
    avatar_url = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    courses = relationship("Course", back_populates="teacher", foreign_keys="[Course.teacher_id]")
    classes_managed = relationship("Class", back_populates="teacher", foreign_keys="[Class.teacher_id]")
    student_classes = relationship("ClassStudent", back_populates="student")
    created_materials = relationship("Material", back_populates="created_by_user", foreign_keys="[Material.created_by_user_id]")
    created_quizzes = relationship("Quiz", back_populates="created_by_user", foreign_keys="[Quiz.created_by_user_id]")
    material_progresses = relationship("StudentMaterialProgress", back_populates="student")
    quiz_attempts = relationship("StudentQuizAttempt", back_populates="student")


# 课程表 (courses) - 教师创建的课程
class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    cover_image_url = Column(String(255))
    teacher_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    status = Column(String(50), default="draft") # e.g., draft, published, archived
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    teacher = relationship("User", back_populates="courses", foreign_keys=[teacher_id])
    materials = relationship("Material", back_populates="course")
    quizzes = relationship("Quiz", back_populates="course")

# 班级表 (classes) - 教师管理的班级
class Class(Base):
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    teacher_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    teacher = relationship("User", back_populates="classes_managed", foreign_keys=[teacher_id])
    students = relationship("ClassStudent", back_populates="class_")


# 班级与学生关联表 (class_students) - 多对多关系
class ClassStudent(Base):
    __tablename__ = "class_students"

    class_id = Column(Integer, ForeignKey('classes.id'), primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), primary_key=True, index=True)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    class_ = relationship("Class", back_populates="students")
    student = relationship("User", back_populates="student_classes")


# 教学资料表 (materials) - 视频、图片、PPT、PDF等
class Material(Base):
    __tablename__ = "materials"

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(Integer, ForeignKey('courses.id'), nullable=False, index=True) # Foreign Key
    title = Column(String(255), nullable=False)
    description = Column(Text)
    file_path_minio = Column(String(255), nullable=False) # Minio存储路径
    file_type = Column(Enum(MaterialType), nullable=False)
    file_size_bytes = Column(Integer)
    duration_seconds = Column(Integer) # Only for video/audio
    preview_url = Column(String(255)) # Optional, Minio presigned URL or thumbnail
    extracted_text_content = Column(Text) # Extracted text content, for Milvus vectorization
    milvus_vector_id = Column(String(255), unique=True) # Milvus ID, used for association
    created_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="materials")
    created_by_user = relationship("User", back_populates="created_materials", foreign_keys=[created_by_user_id])
    material_progresses = relationship("StudentMaterialProgress", back_populates="material")


# 学生学习进度表 (student_material_progress)
class StudentMaterialProgress(Base):
    __tablename__ = "student_material_progress"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    material_id = Column(Integer, ForeignKey('materials.id'), nullable=False, index=True) # Foreign Key
    progress_percentage = Column(Integer, default=0) # 0-100
    last_viewed_timestamp = Column(Integer) # Video: last viewed time (seconds), Document: last viewed page or scroll position
    is_completed = Column(Boolean, default=False)
    total_view_duration_seconds = Column(Integer, default=0) # Total study duration for this material
    last_updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (UniqueConstraint('student_id', 'material_id', name='_student_material_uc'),)

    # Relationships
    student = relationship("User", back_populates="material_progresses")
    material = relationship("Material", back_populates="material_progresses")


# 测验/作业表 (quizzes)
class Quiz(Base):
    __tablename__ = "quizzes"

    id = Column(Integer, primary_key=True, index=True)
    course_id = Column(Integer, ForeignKey('courses.id'), nullable=False, index=True) # Foreign Key
    title = Column(String(255), nullable=False)
    description = Column(Text)
    quiz_type = Column(Enum(QuizType), nullable=False)
    due_date = Column(DateTime(timezone=True))
    is_published = Column(Boolean, default=False)
    created_by_user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    course = relationship("Course", back_populates="quizzes")
    created_by_user = relationship("User", back_populates="created_quizzes", foreign_keys=[created_by_user_id])
    questions = relationship("QuizQuestion", back_populates="quiz")
    attempts = relationship("StudentQuizAttempt", back_populates="quiz")


# 测验问题表 (quiz_questions)
class QuizQuestion(Base):
    __tablename__ = "quiz_questions"

    id = Column(Integer, primary_key=True, index=True)
    quiz_id = Column(Integer, ForeignKey('quizzes.id'), nullable=False, index=True) # Foreign Key
    question_text = Column(Text, nullable=False)
    question_type = Column(Enum(QuestionType), nullable=False)
    options = Column(Text) # Stored as JSON string, e.g., '{"A": "Option A", "B": "Option B"}'
    correct_answer = Column(Text) # Stored as JSON string, e.g., '"A"' or '["A", "B"]'
    score = Column(Integer, default=1)
    question_order = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    quiz = relationship("Quiz", back_populates="questions")


# 学生测验尝试表 (student_quiz_attempts)
class StudentQuizAttempt(Base):
    __tablename__ = "student_quiz_attempts"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True) # Foreign Key
    quiz_id = Column(Integer, ForeignKey('quizzes.id'), nullable=False, index=True) # Foreign Key
    score = Column(Integer) # Total score for the attempt
    submitted_answers = Column(Text) # Stored as JSON string, e.g., '{"question_id_1": "user_answer_1"}'
    start_time = Column(DateTime(timezone=True))
    submit_time = Column(DateTime(timezone=True))
    is_completed = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    student = relationship("User", back_populates="quiz_attempts")
    quiz = relationship("Quiz", back_populates="attempts")


# 通知表 (notifications)
class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    target_user_id = Column(Integer, ForeignKey('users.id'), index=True) # If personal notification
    target_class_id = Column(Integer, ForeignKey('classes.id'), index=True) # If class notification
    target_course_id = Column(Integer, ForeignKey('courses.id'), index=True) # If course notification
    type = Column(Enum(NotificationType), nullable=False)
    title = Column(String(255), nullable=False)
    content = Column(Text)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships (optional, based on need for eager loading)
    # target_user = relationship("User", foreign_keys=[target_user_id])
    # target_class = relationship("Class", foreign_keys=[target_class_id])
    # target_course = relationship("Course", foreign_keys=[target_course_id])