import os
from fastapi import FastAPI, Depends, Request, status
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.orm import Session
from app.database import Base, engine, get_db
from app.auth import router as auth_router
from app.api.v1.routers import users as users_router
from app.api.v1.routers import materials as materials_router
from app.api.v1.routers import courses as courses_router
from app.api.v1.routers import classes as classes_router
from app.api.v1.routers import quizzes as quizzes_router
from app.api.v1.routers import students as students_router
from app.api.v1.routers import notifications as notifications_router
from app.api.v1.routers import reports as reports_router
from app.api.v1.routers import admin as admin_router
from app.models import User, ClassStudent, Class, QuizQuestion, QuestionType, StudentQuizAttempt, Material, Notification # 导入所有相关模型，确保 Base.metadata.create_all 识别
from app.utils.minio_client import ensure_bucket_exists
from app.utils.milvus_client_utils import init_milvus, get_sentence_transformer_model
from app.config import setup_logging # 导入日志设置函数
from app.exceptions import CustomHTTPException, BadRequestError, UnauthorizedError, ForbiddenError, NotFoundError, InternalServerError
from app.schemas import ErrorResponse
import logging

# 获取 app logger
logger = logging.getLogger(__name__)

# 创建数据库表（如果它们不存在）
def create_db_and_tables():
    logger.info("Attempting to create database tables...")
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables creation process initiated.")

app = FastAPI(
    title="Education Platform API",
    description="Backend API for Education Platform (Teacher & Student)",
    version="1.0.0", # 更新版本号
    docs_url="/docs",
    redoc_url="/redoc"
)

# --- 全局异常处理 ---
@app.exception_handler(CustomHTTPException)
async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    """处理所有 CustomHTTPException"""
    log_message = f"Custom HTTP Exception occurred: {exc.detail}, Code: {exc.code}, Status: {exc.status_code}"
    if exc.data:
        log_message += f", Data: {exc.data}"
    logger.error(log_message)
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(detail=exc.detail, code=exc.code, data=exc.data).dict()
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理 Pydantic 验证错误"""
    error_details = exc.errors()
    formatted_errors = [
        {"loc": err["loc"], "msg": err["msg"], "type": err["type"]} for err in error_details
    ]
    logger.warning(f"Validation Error: {formatted_errors}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(detail="Validation error", code="VALIDATION_ERROR", data=formatted_errors).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """捕获所有未被处理的通用异常"""
    logger.exception(f"Unhandled Internal Server Error: {exc}") # 使用 exception 记录栈追踪
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(detail="An unexpected error occurred. Please try again later.", code="INTERNAL_SERVER_ERROR").dict()
    )

# 注册路由
app.include_router(auth_router.router)
app.include_router(users_router.router, prefix="/api/v1")
app.include_router(materials_router.router, prefix="/api/v1")
app.include_router(courses_router.router, prefix="/api/v1")
app.include_router(classes_router.router, prefix="/api/v1")
app.include_router(quizzes_router.router, prefix="/api/v1")
app.include_router(students_router.router, prefix="/api/v1")
app.include_router(notifications_router.router, prefix="/api/v1")
app.include_router(reports_router.router, prefix="/api/v1")
app.include_router(admin_router.router, prefix="/api/v1")

@app.on_event("startup")
async def on_startup():
    """
    应用启动时执行的事件。
    """
    logger.info("Application startup initiated.")
    create_db_and_tables() # 创建数据库表
    ensure_bucket_exists() # 确保 Minio 桶存在
    get_sentence_transformer_model() # 预加载 AI 模型
    init_milvus() # 初始化 Milvus 连接和 Collection
    logger.info("Application started successfully and ready to serve requests.")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """
    根路径欢迎信息。
    """
    return """
    <html>
        <head>
            <title>Education Platform API</title>
        </head>
        <body>
            <h1>Welcome to Education Platform API!</h1>
            <p>Go to <a href="/docs">/docs</a> for API documentation (Swagger UI).</p>
            <p>Go to <a href="/redoc">/redoc</a> for alternative API documentation (ReDoc).</p>
        </body>
    </html>
    """

@app.get("/api/v1/protected-test")
async def protected_test(current_user: User = Depends(get_current_active_user)):
    logger.info(f"Protected test endpoint accessed by {current_user.username} (Role: {current_user.role.value}).")
    return {"message": f"Hello, {current_user.username}! You are a {current_user.role.value}."}


@app.on_event("startup")
async def on_startup():
    logger.info("Application startup initiated.")
    create_db_and_tables() # This automatically creates all tables based on app/models.py

    # --- Initial Data Initialization (Admin User) ---
    db = next(get_db()) # Get a database session for startup operations
    try:
        admin_username = os.getenv("DEFAULT_ADMIN_USERNAME", "admin")
        admin_password = os.getenv("DEFAULT_ADMIN_PASSWORD", "adminpassword")
        admin_email = os.getenv("DEFAULT_ADMIN_EMAIL", "<EMAIL>")
        admin_fullname = os.getenv("DEFAULT_ADMIN_FULLNAME", "System Administrator")

        existing_admin = crud.get_user_by_username(db, admin_username)
        if not existing_admin:
            admin_user_data = UserCreate(
                username=admin_username,
                password=admin_password,
                email=admin_email,
                full_name=admin_fullname,
                role=UserRole.admin
            )
            created_admin = crud.create_user(db, admin_user_data)
            logger.info(f"Default admin user '{created_admin.username}' created successfully.")
            logger.warning(f"Default admin password is '{admin_password}'. PLEASE CHANGE IT IMMEDIATELY AFTER FIRST LOGIN!")
        else:
            logger.info(f"Admin user '{admin_username}' already exists.")
            if existing_admin.role != UserRole.admin: # Ensure role is admin if somehow changed
                existing_admin.role = UserRole.admin
                db.add(existing_admin)
                db.commit()
                db.refresh(existing_admin)
                logger.warning(f"Existing user '{admin_username}' role corrected to 'admin'.")

    except Exception as e:
        logger.critical(f"Failed to initialize default admin user: {e}", exc_info=True)
    finally:
        db.close()
    # --- End of Initial Data Initialization ---

    ensure_bucket_exists() # Ensure Minio bucket exists
    get_sentence_transformer_model() # Pre-load AI model
    init_milvus() # Initialize Milvus connection and Collection
    logger.info("Application started successfully and ready to serve requests.")