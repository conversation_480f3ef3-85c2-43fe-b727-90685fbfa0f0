# app/exceptions.py
from fastapi import HTTPException, status
from typing import Optional, Any

class CustomHTTPException(HTTPException):
    """Custom HTTP Exception with optional code and data."""
    def __init__(self, status_code: int, detail: str, code: Optional[str] = None, data: Optional[Any] = None):
        super().__init__(status_code=status_code, detail=detail)
        self.code = code
        self.data = data

# Define common custom exceptions
class UnauthorizedError(CustomHTTPException):
    def __init__(self, detail: str = "Not authenticated", code: str = "UNAUTHORIZED"):
        super().__init__(status_code=status.HTTP_401_UNAUTHORIZED, detail=detail, code=code)

class ForbiddenError(CustomHTTPException):
    def __init__(self, detail: str = "Not authorized to perform this action", code: str = "FORBIDDEN"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail, code=code)

class NotFoundError(CustomHTTPException):
    def __init__(self, detail: str = "Resource not found", code: str = "NOT_FOUND"):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail, code=code)

class BadRequestError(CustomHTTPException):
    def __init__(self, detail: str = "Bad request", code: str = "BAD_REQUEST", data: Optional[Any] = None):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail, code=code, data=data)

class InternalServerError(CustomHTTPException):
    def __init__(self, detail: str = "Internal server error", code: str = "INTERNAL_SERVER_ERROR"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail, code=code)