# .env - Production Environment Variables

# PostgreSQL Database Configuration (using SQLite for development)
# For development, we'll use SQLite instead of PostgreSQL
DATABASE_URL="sqlite:///./app.db"
# These are kept for reference but not used with SQLite
POSTGRES_DB=your_database
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# FastAPI Application Secret Key for JWT
# !!! IMPORTANT: Replace this with a long, complex, and truly random string in production !!!
SECRET_KEY="a_very_long_and_complex_random_string_for_production_replace_this_with_unique_value"

# Logging Configuration
LOG_LEVEL=INFO # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE_PATH=app/logs/app.log # Path for development
LOG_MAX_BYTES=10485760 # 10 MB
LOG_BACKUP_COUNT=5 # Keep 5 backup log files

# Minio Object Storage Configuration
# This endpoint points to the 'minio' service in Docker Compose
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin # Replace with your Minio access key
MINIO_SECRET_KEY=minioadmin # Replace with your Minio secret key
MINIO_BUCKET_NAME=edu-materials
MINIO_SECURE=False # Set to True if Minio is configured with HTTPS (e.g., via Nginx proxy)

# Milvus Vector Database Configuration
# This URI points to the 'milvus' service in Docker Compose
MILVUS_URI=milvus:19530
EMBEDDING_DIM=384 # Dimension of the sentence-transformers model (all-MiniLM-L6-v2 is 384)