# .env - Production Environment Variables

# PostgreSQL Database Configuration
# This URL will be used by the FastAPI app to connect to the 'db' service in Docker Compose
DATABASE_URL="postgresql+psycopg2://your_user:your_password@db:5432/your_database"
# Ensure these match the values set in docker-compose.yml for the 'db' service
POSTGRES_DB=your_database
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# FastAPI Application Secret Key for JWT
# !!! IMPORTANT: Replace this with a long, complex, and truly random string in production !!!
SECRET_KEY="a_very_long_and_complex_random_string_for_production_replace_this_with_unique_value"

# Logging Configuration
LOG_LEVEL=INFO # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE_PATH=/app/logs/app.log # Path inside the Docker container
LOG_MAX_BYTES=10485760 # 10 MB
LOG_BACKUP_COUNT=5 # Keep 5 backup log files

# Minio Object Storage Configuration
# This endpoint points to the 'minio' service in Docker Compose
MINIO_ENDPOINT=minio:9000
MINIO_ACCESS_KEY=minioadmin # Replace with your Minio access key
MINIO_SECRET_KEY=minioadmin # Replace with your Minio secret key
MINIO_BUCKET_NAME=edu-materials
MINIO_SECURE=False # Set to True if Minio is configured with HTTPS (e.g., via Nginx proxy)

# Milvus Vector Database Configuration
# This URI points to the 'milvus' service in Docker Compose
MILVUS_URI=milvus:19530
EMBEDDING_DIM=384 # Dimension of the sentence-transformers model (all-MiniLM-L6-v2 is 384)
```
**重要提示：** 在部署前，务必替换 `SECRET_KEY`、`POSTGRES_USER`、`POSTGRES_PASSWORD`、`POSTGRES_DB`、`MINIO_ACCESS_KEY`、`MINIO_SECRET_KEY` 为您实际的、安全的生产环境凭据。